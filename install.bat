@echo off
echo ========================================
echo OSLLTN Multi-Boot USB Creator Installer
echo نظام تثبيت إنشاء فلاش USB متعدد الإقلاع
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed / Python غير مثبت
    echo.
    echo Please install Python 3.8+ from:
    echo يرجى تثبيت Python 3.8+ من:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found / تم العثور على Python
python --version

echo.
echo 📦 Installing dependencies / تثبيت التبعيات...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies / فشل في تثبيت التبعيات
    pause
    exit /b 1
)

echo.
echo ✅ Installation completed successfully / تم التثبيت بنجاح
echo.
echo To run the application / لتشغيل التطبيق:
echo   python run.py
echo.
echo Or double-click on: run.bat
echo أو انقر نقراً مزدوجاً على: run.bat
echo.
pause
