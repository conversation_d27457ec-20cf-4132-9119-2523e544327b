menuentry "{OS_NAME}" --class generic --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        echo "Attempting to boot ISO: $iso_path"
        echo "محاولة إقلاع ISO: $iso_path"

        # Try loopback mount first
        loopback loop "$iso_path"

        # Look for common boot files
        if [ -f (loop)/boot/grub/loopback.cfg ]; then
            # GRUB-based ISO
            configfile (loop)/boot/grub/loopback.cfg
        elif [ -f (loop)/isolinux/isolinux.cfg ]; then
            # ISOLINUX-based ISO
            linux (loop)/isolinux/memdisk iso raw
            initrd "$iso_path"
        else
            # Generic chainload
            echo "Using generic chainload method..."
            echo "استخدام طريقة chainload العامة..."
            map "$iso_path" (0xff)
            map --hook
            chainloader (0xff)
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}