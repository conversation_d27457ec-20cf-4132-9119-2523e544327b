@echo off
echo ========================================
echo OSLLTN Multi-Boot USB Creator
echo نظام إنشاء فلاش USB متعدد الإقلاع
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Not running as administrator
    echo ⚠️  تحذير: لا يتم التشغيل كمدير
    echo.
    echo Some features may not work properly.
    echo بعض الميزات قد لا تعمل بشكل صحيح.
    echo.
    echo Right-click and select "Run as administrator"
    echo انقر بالزر الأيمن واختر "تشغيل كمدير"
    echo.
    pause
)

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed / Python غير مثبت
    echo.
    echo Please run install.bat first
    echo يرجى تشغيل install.bat أولاً
    echo.
    pause
    exit /b 1
)

echo 🚀 Starting OSLLTN...
echo 🚀 بدء تشغيل OSLLTN...
echo.

python run.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application exited with error / التطبيق خرج بخطأ
    pause
)

echo.
echo Application closed / تم إغلاق التطبيق
pause
