@echo off
echo ========================================
echo OSLLTN Multi-Boot USB Creator
echo نظام إنشاء فلاش USB متعدد الإقلاع
echo ========================================
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Warning: Not running as administrator
    echo ⚠️  تحذير: لا يتم التشغيل كمدير
    echo.
    echo Some features may not work properly.
    echo بعض الميزات قد لا تعمل بشكل صحيح.
    echo.
    echo Right-click and select "Run as administrator"
    echo انقر بالزر الأيمن واختر "تشغيل كمدير"
    echo.
    echo Continue anyway? (Y/N) / المتابعة على أي حال؟
    set /p choice="Enter your choice / أدخل اختيارك: "
    if /i "%choice%" neq "Y" (
        echo Exiting... / خروج...
        exit /b 1
    )
)

REM Check if Python is installed
echo 🔍 Checking Python installation...
echo 🔍 فحص تثبيت Python...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python is not installed / Python غير مثبت
    echo.
    echo Would you like to install Python automatically? (Y/N)
    echo هل تريد تثبيت Python تلقائياً؟ (Y/N)
    set /p install_choice="Enter your choice / أدخل اختيارك: "
    if /i "%install_choice%" equ "Y" (
        echo.
        echo Running Python installer...
        echo تشغيل مثبت Python...
        call install_python.bat
        exit /b %errorlevel%
    ) else (
        echo.
        echo Please install Python manually:
        echo يرجى تثبيت Python يدوياً:
        echo 1. Go to: https://www.python.org/downloads/
        echo 2. Download and install Python 3.8+
        echo 3. Make sure to check "Add Python to PATH"
        echo 4. Restart this script
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Python found / تم العثور على Python
python --version
echo.

REM Check if requirements are installed
echo 🔍 Checking requirements...
echo 🔍 فحص المتطلبات...

python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Requirements not installed / المتطلبات غير مثبتة
    echo.
    echo Installing requirements automatically...
    echo تثبيت المتطلبات تلقائياً...
    echo.
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ Failed to install requirements / فشل في تثبيت المتطلبات
        echo Please run: pip install -r requirements.txt
        echo يرجى تشغيل: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

echo ✅ Requirements satisfied / المتطلبات متوفرة
echo.

echo 🚀 Starting OSLLTN...
echo 🚀 بدء تشغيل OSLLTN...
echo.

python run.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ Application exited with error / التطبيق خرج بخطأ
    echo.
    echo Common solutions / حلول شائعة:
    echo 1. Run as administrator / شغل كمدير
    echo 2. Check USB device connection / تحقق من اتصال جهاز USB
    echo 3. Install missing dependencies / ثبت التبعيات المفقودة
    echo.
    pause
)

echo.
echo Application closed / تم إغلاق التطبيق
echo Thank you for using OSLLTN! / شكراً لاستخدام OSLLTN!
pause
