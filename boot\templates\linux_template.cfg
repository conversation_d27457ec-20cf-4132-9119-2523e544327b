menuentry "{OS_NAME}" --class linux --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        loopback loop "$iso_path"

        # Try different Linux boot methods
        if [ -f (loop)/casper/vmlinuz ]; then
            # Ubuntu/Debian live
            echo "Booting Ubuntu/Debian live system..."
            echo "إقلاع نظام Ubuntu/Debian المباشر..."
            linux (loop)/casper/vmlinuz boot=casper iso-scan/filename="$iso_path" quiet splash
            initrd (loop)/casper/initrd
        elif [ -f (loop)/live/vmlinuz ]; then
            # Debian live
            echo "Booting Debian live system..."
            echo "إقلاع نظام Debian المباشر..."
            linux (loop)/live/vmlinuz boot=live findiso="$iso_path" quiet splash
            initrd (loop)/live/initrd.img
        elif [ -f (loop)/isolinux/vmlinuz ]; then
            # Generic Linux
            echo "Booting Linux system..."
            echo "إقلاع نظام Linux..."
            linux (loop)/isolinux/vmlinuz root=/dev/ram0 rw
            initrd (loop)/isolinux/initrd.img
        elif [ -f (loop)/images/pxeboot/vmlinuz ]; then
            # Red Hat/Fedora/CentOS
            echo "Booting Red Hat based system..."
            echo "إقلاع نظام مبني على Red Hat..."
            linux (loop)/images/pxeboot/vmlinuz inst.stage2=hd:LABEL=OSLLTN_DATA:"$iso_path"
            initrd (loop)/images/pxeboot/initrd.img
        else
            echo "No compatible Linux boot method found"
            echo "لم يتم العثور على طريقة إقلاع Linux متوافقة"
            echo "Attempting generic ISO boot..."
            echo "محاولة الإقلاع العام للـ ISO..."
            map "$iso_path" (0xff)
            map --hook
            chainloader (0xff)
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}