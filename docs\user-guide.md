# OSLLTN User Guide
## دليل المستخدم لـ OSLLTN

This comprehensive guide will help you create multi-boot USB drives using OSLLTN.

هذا الدليل الشامل سيساعدك في إنشاء فلاش USB متعدد الإقلاع باستخدام OSLLTN.

## Table of Contents / جدول المحتويات

1. [Getting Started / البداية](#getting-started--البداية)
2. [System Requirements / متطلبات النظام](#system-requirements--متطلبات-النظام)
3. [Installation / التثبيت](#installation--التثبيت)
4. [Basic Usage / الاستخدام الأساسي](#basic-usage--الاستخدام-الأساسي)
5. [Advanced Features / الميزات المتقدمة](#advanced-features--الميزات-المتقدمة)
6. [Troubleshooting / استكشاف الأخطاء](#troubleshooting--استكشاف-الأخطاء)

## Getting Started / البداية

### What is OSLLTN? / ما هو OSLLTN؟

OSLLTN (Operating System Live Linux To NTFS) is a powerful tool that allows you to create multi-boot USB drives. You can store multiple operating system images (ISO, WIM, IMG files) on a single USB drive and boot from any of them.

OSLLTN هو أداة قوية تتيح لك إنشاء فلاش USB متعدد الإقلاع. يمكنك تخزين صور أنظمة تشغيل متعددة (ملفات ISO، WIM، IMG) على فلاش USB واحد والإقلاع من أي منها.

### Key Benefits / الفوائد الرئيسية

- **Multiple OS Support** / **دعم أنظمة تشغيل متعددة**: Windows, Linux, and other systems
- **Easy to Use** / **سهل الاستخدام**: Intuitive graphical interface
- **Cross-Platform** / **متعدد المنصات**: Works on Windows, Linux, and macOS
- **Secure Boot Ready** / **جاهز لـ Secure Boot**: Optional Secure Boot support
- **Fast and Reliable** / **سريع وموثوق**: Efficient boot process

## System Requirements / متطلبات النظام

### Minimum Requirements / الحد الأدنى من المتطلبات

- **Operating System** / **نظام التشغيل**:
  - Windows 10 or later / Windows 10 أو أحدث
  - Linux (Ubuntu 18.04+, Debian 10+, Fedora 30+, etc.)
  - macOS 10.14 or later / macOS 10.14 أو أحدث

- **Hardware** / **الأجهزة**:
  - 2 GB RAM minimum / 2 جيجابايت رام كحد أدنى
  - 100 MB free disk space / 100 ميجابايت مساحة فارغة
  - USB 2.0 or higher port / منفذ USB 2.0 أو أعلى
  - USB drive (4 GB minimum) / فلاش USB (4 جيجابايت كحد أدنى)

- **Software** / **البرمجيات**:
  - Python 3.8 or higher / Python 3.8 أو أحدث
  - Administrator/root privileges / صلاحيات المدير/الجذر

### Recommended Requirements / المتطلبات الموصى بها

- **Hardware** / **الأجهزة**:
  - 4 GB RAM or more / 4 جيجابايت رام أو أكثر
  - 500 MB free disk space / 500 ميجابايت مساحة فارغة
  - USB 3.0 or higher / USB 3.0 أو أعلى
  - USB drive (16 GB or larger) / فلاش USB (16 جيجابايت أو أكبر)

- **Additional Software** / **برمجيات إضافية**:
  - QEMU (for testing) / QEMU (للاختبار)
  - OpenSSL (for Secure Boot) / OpenSSL (لـ Secure Boot)

## Installation / التثبيت

### Method 1: Standalone Executable / الطريقة 1: ملف تنفيذي مستقل

1. Download the latest release from [GitHub Releases](https://github.com/oslltn/multiboot-usb-creator/releases)
2. Extract the archive / استخرج الأرشيف
3. Run the executable / شغل الملف التنفيذي:
   - Windows: `oslltn.exe`
   - Linux: `./oslltn`
   - macOS: `./oslltn`

### Method 2: Python Installation / الطريقة 2: تثبيت Python

#### Install from PyPI / التثبيت من PyPI
```bash
pip install oslltn
oslltn-gui
```

#### Install from Source / التثبيت من المصدر
```bash
git clone https://github.com/oslltn/multiboot-usb-creator.git
cd multiboot-usb-creator
pip install -r requirements.txt
python main.py
```

### Platform-Specific Setup / إعداد خاص بالمنصة

#### Windows Setup / إعداد Windows
1. **Run as Administrator** / **تشغيل كمدير**:
   - Right-click on the executable / انقر بالزر الأيمن على الملف التنفيذي
   - Select "Run as administrator" / اختر "تشغيل كمدير"

2. **Install Dependencies** / **تثبيت التبعيات**:
   ```cmd
   pip install pywin32
   ```

#### Linux Setup / إعداد Linux
1. **Install Dependencies** / **تثبيت التبعيات**:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install python3-pyqt6 parted
   
   # Fedora
   sudo dnf install python3-PyQt6 parted
   
   # Arch Linux
   sudo pacman -S python-pyqt6 parted
   ```

2. **Run with sudo** / **تشغيل مع sudo**:
   ```bash
   sudo python main.py
   ```

#### macOS Setup / إعداد macOS
1. **Install Dependencies** / **تثبيت التبعيات**:
   ```bash
   brew install python-tk
   pip install PyQt6
   ```

2. **Grant Disk Access** / **منح صلاحية الوصول للقرص**:
   - Go to System Preferences > Security & Privacy > Privacy
   - Add the application to "Full Disk Access"

## Basic Usage / الاستخدام الأساسي

### Step 1: Launch OSLLTN / الخطوة 1: تشغيل OSLLTN

1. Start the application / ابدأ التطبيق
2. The main window will appear / ستظهر النافذة الرئيسية
3. Wait for USB device detection / انتظر اكتشاف أجهزة USB

### Step 2: Select USB Device / الخطوة 2: اختيار جهاز USB

1. **Connect your USB drive** / **وصل فلاش USB**:
   - Insert the USB drive into your computer / أدخل فلاش USB في الكمبيوتر
   - Wait for it to be detected / انتظر حتى يتم اكتشافه

2. **Select the device** / **اختر الجهاز**:
   - Open the "USB Devices" dropdown / افتح قائمة "أجهزة USB"
   - Select your USB drive / اختر فلاش USB الخاص بك
   - Check the device information / تحقق من معلومات الجهاز

⚠️ **Warning / تحذير**: All data on the USB drive will be erased! / جميع البيانات على فلاش USB ستُمحى!

### Step 3: Configure Settings / الخطوة 3: تكوين الإعدادات

1. **ESP Partition Size** / **حجم قسم ESP**:
   - Default: 512 MB / الافتراضي: 512 ميجابايت
   - Recommended: 512-1024 MB / الموصى به: 512-1024 ميجابايت

2. **Secure Boot** / **Secure Boot**:
   - Enable if you need Secure Boot support / فعل إذا كنت تحتاج دعم Secure Boot
   - Leave disabled for maximum compatibility / اتركه معطلاً للحصول على أقصى توافق

3. **Auto-refresh** / **التحديث التلقائي**:
   - Keep enabled to automatically detect new devices / اتركه مفعلاً لاكتشاف الأجهزة الجديدة تلقائياً

### Step 4: Add ISO Files / الخطوة 4: إضافة ملفات ISO

#### Method A: Add Individual Files / الطريقة أ: إضافة ملفات فردية
1. Click "Add Files" button / انقر على زر "إضافة ملفات"
2. Select your ISO/WIM/IMG files / اختر ملفات ISO/WIM/IMG
3. Click "Open" / انقر على "فتح"

#### Method B: Scan Directory / الطريقة ب: فحص مجلد
1. Click "Scan Directory" button / انقر على زر "فحص مجلد"
2. Select a folder containing ISO files / اختر مجلد يحتوي على ملفات ISO
3. Wait for scanning to complete / انتظر حتى يكتمل الفحص

### Step 5: Verify Files / الخطوة 5: التحقق من الملفات

1. **Review the file list** / **راجع قائمة الملفات**:
   - Check that all files are detected correctly / تحقق من اكتشاف جميع الملفات بشكل صحيح
   - Verify OS type and architecture / تحقق من نوع نظام التشغيل والمعمارية

2. **Calculate checksums (optional)** / **حساب checksums (اختياري)**:
   - Click "Calculate Checksums" / انقر على "حساب Checksums"
   - This will verify file integrity / هذا سيتحقق من سلامة الملفات
   - ⏱️ This may take time for large files / قد يستغرق وقتاً للملفات الكبيرة

### Step 6: Prepare USB Device / الخطوة 6: تحضير جهاز USB

1. **Start preparation** / **ابدأ التحضير**:
   - Click "Prepare USB Device" / انقر على "تحضير جهاز USB"
   - Confirm the warning dialog / أكد نافذة التحذير
   - Wait for partitioning to complete / انتظر حتى يكتمل التقسيم

2. **What happens during preparation** / **ما يحدث أثناء التحضير**:
   - USB drive is partitioned (GPT) / يتم تقسيم فلاش USB (GPT)
   - ESP partition is created (FAT32) / يتم إنشاء قسم ESP (FAT32)
   - Data partition is created (exFAT) / يتم إنشاء قسم البيانات (exFAT)

### Step 7: Install Bootloader / الخطوة 7: تثبيت محمل الإقلاع

1. **Install GRUB2** / **تثبيت GRUB2**:
   - Click "Install Bootloader" / انقر على "تثبيت محمل الإقلاع"
   - Wait for installation to complete / انتظر حتى يكتمل التثبيت

2. **What gets installed** / **ما يتم تثبيته**:
   - GRUB2 EFI files / ملفات GRUB2 EFI
   - wimboot for Windows support / wimboot لدعم Windows
   - Basic configuration files / ملفات التكوين الأساسية

### Step 8: Generate Boot Menu / الخطوة 8: إنشاء قائمة الإقلاع

1. **Create boot configuration** / **إنشاء تكوين الإقلاع**:
   - Click "Generate Boot Menu" / انقر على "إنشاء قائمة الإقلاع"
   - Wait for configuration generation / انتظر إنشاء التكوين

2. **Copy ISO files** / **نسخ ملفات ISO**:
   - Manually copy your ISO files to the data partition / انسخ ملفات ISO يدوياً إلى قسم البيانات
   - Or use the built-in file manager / أو استخدم مدير الملفات المدمج

### Step 9: Test Your USB / الخطوة 9: اختبار فلاش USB

#### Option A: QEMU Testing / الخيار أ: اختبار QEMU
1. Click "Test in QEMU" / انقر على "اختبار في QEMU"
2. Configure test settings / كوّن إعدادات الاختبار
3. Start virtual machine / ابدأ الآلة الافتراضية

#### Option B: Real Hardware Testing / الخيار ب: اختبار على أجهزة حقيقية
1. Safely eject the USB drive / أخرج فلاش USB بأمان
2. Boot from USB on target computer / أقلع من USB على الكمبيوتر المستهدف
3. Test different operating systems / اختبر أنظمة تشغيل مختلفة

## Advanced Features / الميزات المتقدمة

### Checksum Verification / التحقق من Checksum

Checksums help verify file integrity and detect corruption.

تساعد Checksums في التحقق من سلامة الملفات واكتشاف التلف.

#### Supported Algorithms / الخوارزميات المدعومة
- **MD5**: Fast but less secure / سريع لكن أقل أماناً
- **SHA1**: Good balance / توازن جيد
- **SHA256**: Most secure / الأكثر أماناً

#### How to Use / كيفية الاستخدام
1. Select files in the ISO list / اختر الملفات في قائمة ISO
2. Click "Calculate Checksums" / انقر على "حساب Checksums"
3. Wait for calculation to complete / انتظر حتى يكتمل الحساب
4. Checksums are stored and displayed / يتم تخزين وعرض Checksums

### QEMU Testing / اختبار QEMU

Test your multi-boot USB without rebooting your computer.

اختبر فلاش USB متعدد الإقلاع دون إعادة تشغيل الكمبيوتر.

#### Prerequisites / المتطلبات المسبقة
1. Install QEMU:
   - Windows: Download from [qemu.org](https://www.qemu.org/download/#windows)
   - Linux: `sudo apt-get install qemu-system-x86`
   - macOS: `brew install qemu`

#### Testing Process / عملية الاختبار
1. Click "Test in QEMU" / انقر على "اختبار في QEMU"
2. Configure test settings:
   - Memory: 2048 MB (recommended) / الذاكرة: 2048 ميجابايت (موصى به)
   - Boot mode: UEFI or BIOS / وضع الإقلاع: UEFI أو BIOS
   - Enable KVM (Linux only) / تفعيل KVM (Linux فقط)
3. Start the test / ابدأ الاختبار
4. Interact with the virtual machine / تفاعل مع الآلة الافتراضية

### Secure Boot Support / دعم Secure Boot

Enable Secure Boot compatibility for enhanced security.

فعل توافق Secure Boot لأمان محسن.

#### Requirements / المتطلبات
- UEFI system with Secure Boot / نظام UEFI مع Secure Boot
- OpenSSL installed / OpenSSL مثبت
- Signing certificates / شهادات التوقيع

#### Setup Process / عملية الإعداد
1. **Generate signing keys** / **إنشاء مفاتيح التوقيع**:
   ```bash
   python -c "from utils.secure_boot import SecureBootManager; sbm = SecureBootManager(); sbm.generate_signing_keys(Path('keys'))"
   ```

2. **Enable Secure Boot in settings** / **تفعيل Secure Boot في الإعدادات**:
   - Check "Enable Secure Boot" / ضع علامة على "تفعيل Secure Boot"
   - Specify certificate paths / حدد مسارات الشهادات

3. **Install signed bootloader** / **تثبيت محمل إقلاع موقع**:
   - The bootloader will be automatically signed / سيتم توقيع محمل الإقلاع تلقائياً

### Custom GRUB Configuration / تكوين GRUB مخصص

Customize the boot menu appearance and behavior.

خصص مظهر وسلوك قائمة الإقلاع.

#### Configuration Files / ملفات التكوين
- `boot/templates/main_template.cfg`: Main configuration / التكوين الرئيسي
- `boot/templates/windows_template.cfg`: Windows entries / إدخالات Windows
- `boot/templates/linux_template.cfg`: Linux entries / إدخالات Linux
- `boot/templates/generic_template.cfg`: Generic entries / إدخالات عامة

#### Customization Options / خيارات التخصيص
- **Timeout**: Boot menu timeout / مهلة قائمة الإقلاع
- **Colors**: Menu colors and theme / ألوان القائمة والثيم
- **Background**: Custom background image / صورة خلفية مخصصة
- **Languages**: Menu language / لغة القائمة

### Batch Operations / العمليات المجمعة

Process multiple USB drives or ISO files efficiently.

معالجة عدة فلاشات USB أو ملفات ISO بكفاءة.

#### Batch USB Preparation / تحضير USB مجمع
1. Connect multiple USB drives / وصل عدة فلاشات USB
2. Select all devices / اختر جميع الأجهزة
3. Apply same configuration / طبق نفس التكوين
4. Process in sequence / عالج بالتسلسل

#### Batch ISO Processing / معالجة ISO مجمعة
1. Scan multiple directories / افحص عدة مجلدات
2. Filter by OS type / رشح حسب نوع نظام التشغيل
3. Calculate all checksums / احسب جميع Checksums
4. Generate unified menu / أنشئ قائمة موحدة

## Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

#### Issue: USB Device Not Detected / المشكلة: لا يتم اكتشاف جهاز USB

**Symptoms / الأعراض**:
- USB drive not appearing in device list / فلاش USB لا يظهر في قائمة الأجهزة
- "No USB devices found" message / رسالة "لم يتم العثور على أجهزة USB"

**Solutions / الحلول**:
1. **Check USB connection** / **تحقق من اتصال USB**:
   - Try different USB ports / جرب منافذ USB مختلفة
   - Use USB 2.0 port if USB 3.0 doesn't work / استخدم منفذ USB 2.0 إذا لم يعمل USB 3.0
   - Test with different USB cable / اختبر بكابل USB مختلف

2. **Run as administrator** / **شغل كمدير**:
   - Windows: Right-click → "Run as administrator"
   - Linux: Use `sudo python main.py`
   - macOS: Grant "Full Disk Access" permission

3. **Refresh device list** / **حدث قائمة الأجهزة**:
   - Press F5 or click "Refresh" / اضغط F5 أو انقر "تحديث"
   - Wait 5-10 seconds for detection / انتظر 5-10 ثوان للاكتشاف

4. **Check USB drive health** / **تحقق من صحة فلاش USB**:
   - Test on different computer / اختبر على كمبيوتر مختلف
   - Use disk management tools / استخدم أدوات إدارة الأقراص

#### Issue: Boot Menu Not Appearing / المشكلة: قائمة الإقلاع لا تظهر

**Symptoms / الأعراض**:
- Computer boots to normal OS / الكمبيوتر يقلع إلى نظام التشغيل العادي
- Black screen or error message / شاشة سوداء أو رسالة خطأ
- USB not recognized as bootable / USB غير معترف به كقابل للإقلاع

**Solutions / الحلول**:
1. **Check BIOS/UEFI settings** / **تحقق من إعدادات BIOS/UEFI**:
   - Enable USB boot / فعل إقلاع USB
   - Set USB as first boot device / اجعل USB أول جهاز إقلاع
   - Disable Fast Boot / عطل الإقلاع السريع

2. **Try different boot modes** / **جرب أوضاع إقلاع مختلفة**:
   - Switch between UEFI and Legacy / بدل بين UEFI و Legacy
   - Disable Secure Boot temporarily / عطل Secure Boot مؤقتاً

3. **Verify bootloader installation** / **تحقق من تثبيت محمل الإقلاع**:
   - Click "Verify Installation" / انقر على "التحقق من التثبيت"
   - Reinstall bootloader if needed / أعد تثبيت محمل الإقلاع إذا لزم الأمر

4. **Check USB drive format** / **تحقق من تنسيق فلاش USB**:
   - Ensure proper partitioning / تأكد من التقسيم الصحيح
   - Verify ESP partition exists / تحقق من وجود قسم ESP

#### Issue: ISO Files Not Booting / المشكلة: ملفات ISO لا تقلع

**Symptoms / الأعراض**:
- Boot menu appears but OS doesn't start / قائمة الإقلاع تظهر لكن نظام التشغيل لا يبدأ
- Error messages during boot / رسائل خطأ أثناء الإقلاع
- Kernel panic or blue screen / ذعر النواة أو الشاشة الزرقاء

**Solutions / الحلول**:
1. **Verify ISO integrity** / **تحقق من سلامة ISO**:
   - Calculate and verify checksums / احسب وتحقق من Checksums
   - Re-download corrupted files / أعد تحميل الملفات التالفة

2. **Check ISO compatibility** / **تحقق من توافق ISO**:
   - Ensure ISO is bootable / تأكد من أن ISO قابل للإقلاع
   - Test with known working ISOs / اختبر مع ISOs معروفة العمل

3. **Update boot configuration** / **حدث تكوين الإقلاع**:
   - Regenerate boot menu / أعد إنشاء قائمة الإقلاع
   - Check GRUB configuration syntax / تحقق من تركيب تكوين GRUB

4. **Try different boot parameters** / **جرب معاملات إقلاع مختلفة**:
   - Edit GRUB entry manually / عدل إدخال GRUB يدوياً
   - Add compatibility parameters / أضف معاملات التوافق

#### Issue: Slow Performance / المشكلة: أداء بطيء

**Symptoms / الأعراض**:
- Long boot times / أوقات إقلاع طويلة
- Slow file operations / عمليات ملفات بطيئة
- Application freezing / تجمد التطبيق

**Solutions / الحلول**:
1. **Use faster USB drive** / **استخدم فلاش USB أسرع**:
   - USB 3.0 or higher / USB 3.0 أو أعلى
   - High-quality flash memory / ذاكرة فلاش عالية الجودة

2. **Optimize settings** / **حسن الإعدادات**:
   - Reduce ESP partition size / قلل حجم قسم ESP
   - Disable unnecessary features / عطل الميزات غير الضرورية

3. **Check system resources** / **تحقق من موارد النظام**:
   - Close other applications / أغلق التطبيقات الأخرى
   - Ensure sufficient RAM / تأكد من وجود رام كافية

### Error Messages / رسائل الخطأ

#### "Permission Denied" / "تم رفض الإذن"
- **Cause**: Insufficient privileges / السبب: صلاحيات غير كافية
- **Solution**: Run as administrator/root / الحل: شغل كمدير/جذر

#### "Device Busy" / "الجهاز مشغول"
- **Cause**: USB drive in use / السبب: فلاش USB قيد الاستخدام
- **Solution**: Unmount/eject properly / الحل: ألغ التركيب/أخرج بشكل صحيح

#### "Invalid ISO File" / "ملف ISO غير صالح"
- **Cause**: Corrupted or non-bootable ISO / السبب: ISO تالف أو غير قابل للإقلاع
- **Solution**: Verify and re-download ISO / الحل: تحقق وأعد تحميل ISO

#### "GRUB Installation Failed" / "فشل تثبيت GRUB"
- **Cause**: Partition or filesystem issues / السبب: مشاكل في التقسيم أو نظام الملفات
- **Solution**: Repartition USB drive / الحل: أعد تقسيم فلاش USB

### Getting Help / الحصول على المساعدة

If you continue to experience issues:

إذا استمرت المشاكل:

1. **Check the FAQ** / **راجع الأسئلة الشائعة**: [FAQ](faq.md)
2. **Search existing issues** / **ابحث في المشاكل الموجودة**: [GitHub Issues](https://github.com/oslltn/multiboot-usb-creator/issues)
3. **Create a new issue** / **أنشئ مشكلة جديدة**: Include system info and error logs / أدرج معلومات النظام وسجلات الأخطاء
4. **Join the community** / **انضم للمجتمع**: [Discussions](https://github.com/oslltn/multiboot-usb-creator/discussions)

### Log Files / ملفات السجل

Log files help diagnose issues:

ملفات السجل تساعد في تشخيص المشاكل:

- **Location / الموقع**: `logs/oslltn.log`
- **Content / المحتوى**: Application events and errors / أحداث التطبيق والأخطاء
- **Usage / الاستخدام**: Include in bug reports / أدرج في تقارير الأخطاء

---

This concludes the OSLLTN User Guide. For more advanced topics, see the [Developer Guide](developer-guide.md).

هذا يختتم دليل المستخدم لـ OSLLTN. للمواضيع المتقدمة أكثر، راجع [دليل المطور](developer-guide.md).
