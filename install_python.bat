@echo off
echo ========================================
echo OSLLTN - Python Installation Script
echo سكريبت تثبيت Python لـ OSLLTN
echo ========================================
echo.

REM Check if Python is already installed
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Python is already installed / Python مثبت بالفعل
    python --version
    echo.
    echo Proceeding to install OSLLTN requirements...
    echo المتابعة لتثبيت متطلبات OSLLTN...
    goto install_requirements
)

echo ❌ Python is not installed / Python غير مثبت
echo.
echo This script will help you install Python automatically.
echo هذا السكريبت سيساعدك في تثبيت Python تلقائياً.
echo.

REM Check if winget is available (Windows 10/11)
winget --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 📦 Installing Python using winget...
    echo 📦 تثبيت Python باستخدام winget...
    winget install Python.Python.3.11
    if %errorlevel% equ 0 (
        echo ✅ Python installed successfully! / تم تثبيت Python بنجاح!
        echo.
        echo Please restart this script or run: install.bat
        echo يرجى إعادة تشغيل هذا السكريبت أو تشغيل: install.bat
        pause
        exit /b 0
    ) else (
        echo ❌ winget installation failed / فشل التثبيت باستخدام winget
        goto manual_install
    )
) else (
    echo ⚠️  winget not available / winget غير متوفر
    goto manual_install
)

:manual_install
echo.
echo 📥 Manual Installation Required / التثبيت اليدوي مطلوب
echo.
echo Please follow these steps / يرجى اتباع هذه الخطوات:
echo.
echo 1. Go to: https://www.python.org/downloads/
echo    اذهب إلى: https://www.python.org/downloads/
echo.
echo 2. Download Python 3.11 or later
echo    حمل Python 3.11 أو أحدث
echo.
echo 3. Run the installer and CHECK "Add Python to PATH"
echo    شغل المثبت وضع علامة على "Add Python to PATH"
echo.
echo 4. After installation, restart this script
echo    بعد التثبيت، أعد تشغيل هذا السكريبت
echo.

REM Try to open the Python download page
start https://www.python.org/downloads/

echo Opening Python download page...
echo فتح صفحة تحميل Python...
echo.
pause
exit /b 1

:install_requirements
echo.
echo 📦 Installing OSLLTN requirements...
echo 📦 تثبيت متطلبات OSLLTN...
echo.

REM Check if requirements.txt exists
if not exist requirements.txt (
    echo ❌ requirements.txt not found / ملف requirements.txt غير موجود
    echo Make sure you're in the OSLLTN directory
    echo تأكد من أنك في مجلد OSLLTN
    pause
    exit /b 1
)

REM Install requirements
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

if %errorlevel% equ 0 (
    echo.
    echo ✅ All requirements installed successfully!
    echo ✅ تم تثبيت جميع المتطلبات بنجاح!
    echo.
    echo 🚀 Ready to run OSLLTN!
    echo 🚀 جاهز لتشغيل OSLLTN!
    echo.
    echo To start the application, run: run.bat
    echo لبدء التطبيق، شغل: run.bat
    echo.
    echo Or run as administrator: python run.py
    echo أو شغل كمدير: python run.py
    echo.
) else (
    echo.
    echo ❌ Failed to install requirements / فشل في تثبيت المتطلبات
    echo.
    echo Try running as administrator or check your internet connection
    echo جرب التشغيل كمدير أو تحقق من اتصال الإنترنت
    echo.
)

pause
