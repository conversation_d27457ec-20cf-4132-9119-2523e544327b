#!/bin/bash

echo "========================================"
echo "OSLLTN Multi-Boot USB Creator"
echo "نظام إنشاء فلاش USB متعدد الإقلاع"
echo "========================================"
echo

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "⚠️  Warning: Not running as root"
    echo "⚠️  تحذير: لا يتم التشغيل كجذر"
    echo
    echo "Some features may not work properly."
    echo "بعض الميزات قد لا تعمل بشكل صحيح."
    echo
    echo "Consider running with: sudo ./run.sh"
    echo "فكر في التشغيل باستخدام: sudo ./run.sh"
    echo
    read -p "Continue anyway? (y/N) / المتابعة على أي حال؟ (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed / Python 3 غير مثبت"
    echo
    echo "Please run install.sh first"
    echo "يرجى تشغيل install.sh أولاً"
    echo
    exit 1
fi

echo "🚀 Starting OSLLTN..."
echo "🚀 بدء تشغيل OSLLTN..."
echo

python3 run.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ Application exited with error / التطبيق خرج بخطأ"
    read -p "Press Enter to continue / اضغط Enter للمتابعة..."
fi

echo
echo "Application closed / تم إغلاق التطبيق"
