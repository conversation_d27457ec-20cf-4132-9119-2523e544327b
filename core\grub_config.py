"""
GRUB Configuration Generator
مولد تكوين GRUB

Generates dynamic GRUB configuration files based on detected ISO/WIM files
ينشئ ملفات تكوين GRUB ديناميكية بناءً على ملفات ISO/WIM المكتشفة
"""

import os
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from core.iso_manager import ISOMetadata

logger = logging.getLogger(__name__)

class GRUBConfigGenerator:
    """Generates GRUB configuration files"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.templates_dir = self.project_root / "boot" / "templates"
        self.templates_dir.mkdir(parents=True, exist_ok=True)

        # Create default templates
        self._create_default_templates()

        logger.info("Initialized GRUBConfigGenerator")

    def _create_default_templates(self):
        """Create default GRUB menu templates"""

        # Main template
        main_template = '''# OSLLTN Multi-Boot USB Configuration
# تكوين فلاش USB متعدد الإقلاع
# Generated automatically - Do not edit manually
# تم إنشاؤه تلقائياً - لا تقم بالتعديل يدوياً

set timeout=10
set default=0

# Load video modules
insmod all_video
insmod gfxterm
insmod png
insmod jpeg

# Set graphics mode
set gfxmode=auto
terminal_output gfxterm

# Set theme colors
set color_normal=white/black
set color_highlight=black/light-cyan
set menu_color_normal=white/black
set menu_color_highlight=black/light-cyan

# Load additional modules
insmod part_gpt
insmod part_msdos
insmod fat
insmod exfat
insmod ntfs
insmod ext2
insmod iso9660
insmod loopback
insmod chain

# Set root device (will be set dynamically)
# تعيين الجهاز الجذر (سيتم تعيينه ديناميكياً)
search --no-floppy --fs-uuid --set=root {USB_UUID}

# Main menu header
menuentry "OSLLTN Multi-Boot USB Creator" --class oslltn {
    echo "OSLLTN Multi-Boot USB Creator"
    echo "نظام إنشاء فلاش USB متعدد الإقلاع"
    echo ""
    echo "Select an operating system to boot:"
    echo "اختر نظام التشغيل للإقلاع:"
    echo ""
    sleep 3
}

menuentry "--- Operating Systems ---" --class separator {
    true
}

{BOOT_ENTRIES}

menuentry "--- Advanced Options ---" --class separator {
    true
}

submenu "System Tools" --class tools {
    menuentry "Memory Test (memtest86+)" --class memtest {
        if [ -f /boot/memtest86+.bin ]; then
            linux16 /boot/memtest86+.bin
        else
            echo "Memory test not available"
            echo "اختبار الذاكرة غير متوفر"
            sleep 2
        fi
    }

    menuentry "Hardware Detection Tool" --class hardware {
        if [ -f /boot/hdt.c32 ]; then
            linux /boot/hdt.c32
        else
            echo "Hardware detection tool not available"
            echo "أداة اكتشاف الأجهزة غير متوفرة"
            sleep 2
        fi
    }

    menuentry "System Information" --class info {
        echo "Platform: $grub_platform"
        echo "CPU: $grub_cpu"
        echo "Memory: Available"
        echo ""
        echo "المنصة: $grub_platform"
        echo "المعالج: $grub_cpu"
        echo "الذاكرة: متوفرة"
        sleep 5
    }
}

submenu "Boot Options" --class options {
    menuentry "Boot from Hard Disk" --class harddisk {
        echo "Booting from first hard disk..."
        echo "الإقلاع من القرص الصلب الأول..."
        set root=(hd1)
        chainloader +1
        boot
    }

    menuentry "Boot from Network (PXE)" --class network {
        echo "Attempting network boot..."
        echo "محاولة الإقلاع من الشبكة..."
        pxe_boot
    }
}

submenu "System Control" --class control {
    menuentry "Reboot" --class reboot {
        echo "Rebooting system..."
        echo "إعادة تشغيل النظام..."
        reboot
    }

    menuentry "Shutdown" --class shutdown {
        echo "Shutting down system..."
        echo "إيقاف تشغيل النظام..."
        halt
    }

    menuentry "UEFI Firmware Settings" --class firmware {
        echo "Entering UEFI firmware settings..."
        echo "الدخول إلى إعدادات UEFI..."
        fwsetup
    }
}
'''

        template_file = self.templates_dir / "main_template.cfg"
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(main_template)

        # Windows boot template
        windows_template = '''menuentry "{OS_NAME}" --class windows --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        loopback loop "$iso_path"

        # Try different Windows boot methods
        if [ -f (loop)/sources/boot.wim ]; then
            # Windows Vista/7/8/10/11 with boot.wim
            echo "Booting Windows with boot.wim..."
            echo "إقلاع Windows مع boot.wim..."

            # Load wimboot
            if [ -f /EFI/OSLLTN/wimboot ]; then
                linux /EFI/OSLLTN/wimboot
                initrd (loop)/sources/boot.wim boot.wim
            else
                echo "wimboot not found!"
                echo "wimboot غير موجود!"
                sleep 3
            fi
        elif [ -f (loop)/bootmgr ]; then
            # Legacy Windows boot
            echo "Booting Windows with bootmgr..."
            echo "إقلاع Windows مع bootmgr..."
            chainloader (loop)/bootmgr
        else
            echo "No compatible Windows boot method found"
            echo "لم يتم العثور على طريقة إقلاع Windows متوافقة"
            sleep 3
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}'''

        windows_template_file = self.templates_dir / "windows_template.cfg"
        with open(windows_template_file, 'w', encoding='utf-8') as f:
            f.write(windows_template)

        # Linux boot template
        linux_template = '''menuentry "{OS_NAME}" --class linux --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        loopback loop "$iso_path"

        # Try different Linux boot methods
        if [ -f (loop)/casper/vmlinuz ]; then
            # Ubuntu/Debian live
            echo "Booting Ubuntu/Debian live system..."
            echo "إقلاع نظام Ubuntu/Debian المباشر..."
            linux (loop)/casper/vmlinuz boot=casper iso-scan/filename="$iso_path" quiet splash
            initrd (loop)/casper/initrd
        elif [ -f (loop)/live/vmlinuz ]; then
            # Debian live
            echo "Booting Debian live system..."
            echo "إقلاع نظام Debian المباشر..."
            linux (loop)/live/vmlinuz boot=live findiso="$iso_path" quiet splash
            initrd (loop)/live/initrd.img
        elif [ -f (loop)/isolinux/vmlinuz ]; then
            # Generic Linux
            echo "Booting Linux system..."
            echo "إقلاع نظام Linux..."
            linux (loop)/isolinux/vmlinuz root=/dev/ram0 rw
            initrd (loop)/isolinux/initrd.img
        elif [ -f (loop)/images/pxeboot/vmlinuz ]; then
            # Red Hat/Fedora/CentOS
            echo "Booting Red Hat based system..."
            echo "إقلاع نظام مبني على Red Hat..."
            linux (loop)/images/pxeboot/vmlinuz inst.stage2=hd:LABEL=OSLLTN_DATA:"$iso_path"
            initrd (loop)/images/pxeboot/initrd.img
        else
            echo "No compatible Linux boot method found"
            echo "لم يتم العثور على طريقة إقلاع Linux متوافقة"
            echo "Attempting generic ISO boot..."
            echo "محاولة الإقلاع العام للـ ISO..."
            map "$iso_path" (0xff)
            map --hook
            chainloader (0xff)
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}'''

        linux_template_file = self.templates_dir / "linux_template.cfg"
        with open(linux_template_file, 'w', encoding='utf-8') as f:
            f.write(linux_template)

        # Generic boot template
        generic_template = '''menuentry "{OS_NAME}" --class generic --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        echo "Attempting to boot ISO: $iso_path"
        echo "محاولة إقلاع ISO: $iso_path"

        # Try loopback mount first
        loopback loop "$iso_path"

        # Look for common boot files
        if [ -f (loop)/boot/grub/loopback.cfg ]; then
            # GRUB-based ISO
            configfile (loop)/boot/grub/loopback.cfg
        elif [ -f (loop)/isolinux/isolinux.cfg ]; then
            # ISOLINUX-based ISO
            linux (loop)/isolinux/memdisk iso raw
            initrd "$iso_path"
        else
            # Generic chainload
            echo "Using generic chainload method..."
            echo "استخدام طريقة chainload العامة..."
            map "$iso_path" (0xff)
            map --hook
            chainloader (0xff)
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}'''

        generic_template_file = self.templates_dir / "generic_template.cfg"
        with open(generic_template_file, 'w', encoding='utf-8') as f:
            f.write(generic_template)

        logger.info("Created default GRUB templates")

    def generate_config(self, iso_files: List[ISOMetadata],
                       usb_uuid: str = "AUTO") -> str:
        """
        Generate complete GRUB configuration
        إنشاء تكوين GRUB كامل

        Args:
            iso_files: List of ISO metadata
            usb_uuid: USB device UUID

        Returns:
            Complete GRUB configuration string
        """
        try:
            logger.info(f"Generating GRUB config for {len(iso_files)} files")

            # Load main template
            main_template_file = self.templates_dir / "main_template.cfg"
            with open(main_template_file, 'r', encoding='utf-8') as f:
                main_template = f.read()

            # Generate boot entries
            boot_entries = []

            # Group files by OS type
            windows_files = [f for f in iso_files if f.os_type == 'windows']
            linux_files = [f for f in iso_files if f.os_type == 'linux']
            other_files = [f for f in iso_files if f.os_type not in ['windows', 'linux']]

            # Add Windows entries
            if windows_files:
                boot_entries.append('# Windows Operating Systems')
                boot_entries.append('# أنظمة تشغيل Windows')
                for iso_file in windows_files:
                    entry = self._generate_windows_entry(iso_file)
                    boot_entries.append(entry)
                boot_entries.append('')

            # Add Linux entries
            if linux_files:
                boot_entries.append('# Linux Operating Systems')
                boot_entries.append('# أنظمة تشغيل Linux')
                for iso_file in linux_files:
                    entry = self._generate_linux_entry(iso_file)
                    boot_entries.append(entry)
                boot_entries.append('')

            # Add other entries
            if other_files:
                boot_entries.append('# Other Operating Systems')
                boot_entries.append('# أنظمة تشغيل أخرى')
                for iso_file in other_files:
                    entry = self._generate_generic_entry(iso_file)
                    boot_entries.append(entry)
                boot_entries.append('')

            # Replace placeholders in main template
            config = main_template.replace('{USB_UUID}', usb_uuid)
            config = config.replace('{BOOT_ENTRIES}', '\n'.join(boot_entries))

            logger.info("GRUB configuration generated successfully")
            return config

        except Exception as e:
            logger.error(f"Error generating GRUB config: {e}")
            return self._generate_fallback_config()

    def _generate_windows_entry(self, iso_file: ISOMetadata) -> str:
        """Generate Windows boot entry"""
        try:
            template_file = self.templates_dir / "windows_template.cfg"
            with open(template_file, 'r', encoding='utf-8') as f:
                template = f.read()

            # Convert absolute path to relative path for GRUB
            iso_path = self._get_grub_path(iso_file.filepath)

            entry = template.replace('{OS_NAME}', iso_file.os_name)
            entry = entry.replace('{ISO_PATH}', iso_path)

            return entry

        except Exception as e:
            logger.error(f"Error generating Windows entry: {e}")
            return self._generate_generic_entry(iso_file)

    def _generate_linux_entry(self, iso_file: ISOMetadata) -> str:
        """Generate Linux boot entry"""
        try:
            template_file = self.templates_dir / "linux_template.cfg"
            with open(template_file, 'r', encoding='utf-8') as f:
                template = f.read()

            # Convert absolute path to relative path for GRUB
            iso_path = self._get_grub_path(iso_file.filepath)

            entry = template.replace('{OS_NAME}', iso_file.os_name)
            entry = entry.replace('{ISO_PATH}', iso_path)

            return entry

        except Exception as e:
            logger.error(f"Error generating Linux entry: {e}")
            return self._generate_generic_entry(iso_file)

    def _generate_generic_entry(self, iso_file: ISOMetadata) -> str:
        """Generate generic boot entry"""
        try:
            template_file = self.templates_dir / "generic_template.cfg"
            with open(template_file, 'r', encoding='utf-8') as f:
                template = f.read()

            # Convert absolute path to relative path for GRUB
            iso_path = self._get_grub_path(iso_file.filepath)

            entry = template.replace('{OS_NAME}', iso_file.os_name)
            entry = entry.replace('{ISO_PATH}', iso_path)

            return entry

        except Exception as e:
            logger.error(f"Error generating generic entry: {e}")
            return f'# Error generating entry for {iso_file.filename}'

    def _get_grub_path(self, file_path: str) -> str:
        """
        Convert file system path to GRUB path
        تحويل مسار نظام الملفات إلى مسار GRUB
        """
        try:
            path = Path(file_path)

            # For USB drives, files should be in the data partition
            # We'll use relative paths from the data partition root
            filename = path.name

            # GRUB path should be relative to the data partition
            return f"/{filename}"

        except Exception as e:
            logger.error(f"Error converting path to GRUB format: {e}")
            return file_path

    def _generate_fallback_config(self) -> str:
        """Generate fallback GRUB configuration"""
        return '''# OSLLTN Multi-Boot USB - Fallback Configuration
# تكوين احتياطي لفلاش USB متعدد الإقلاع

set timeout=10
set default=0

menuentry "OSLLTN Multi-Boot USB" {
    echo "OSLLTN Multi-Boot USB Creator"
    echo "نظام إنشاء فلاش USB متعدد الإقلاع"
    echo ""
    echo "Error: Could not generate boot menu"
    echo "خطأ: لا يمكن إنشاء قائمة الإقلاع"
    echo ""
    echo "Please check your ISO files and try again"
    echo "يرجى التحقق من ملفات ISO والمحاولة مرة أخرى"
    sleep 5
}

menuentry "Reboot" {
    reboot
}

menuentry "Shutdown" {
    halt
}
'''

    def save_config(self, config: str, esp_partition: str) -> bool:
        """
        Save GRUB configuration to ESP partition
        حفظ تكوين GRUB في قسم ESP

        Args:
            config: GRUB configuration string
            esp_partition: Path to ESP partition

        Returns:
            True if successful, False otherwise
        """
        try:
            esp_path = Path(esp_partition)
            grub_cfg_path = esp_path / "EFI" / "OSLLTN" / "grub.cfg"

            # Ensure directory exists
            grub_cfg_path.parent.mkdir(parents=True, exist_ok=True)

            # Write configuration
            with open(grub_cfg_path, 'w', encoding='utf-8') as f:
                f.write(config)

            logger.info(f"GRUB configuration saved to: {grub_cfg_path}")
            return True

        except Exception as e:
            logger.error(f"Error saving GRUB configuration: {e}")
            return False

    def validate_config(self, config: str) -> Tuple[bool, List[str]]:
        """
        Validate GRUB configuration syntax
        التحقق من صحة تركيب تكوين GRUB

        Args:
            config: GRUB configuration string

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []

        try:
            # Basic syntax checks
            lines = config.split('\n')

            menuentry_count = 0
            brace_count = 0

            for line_num, line in enumerate(lines, 1):
                line = line.strip()

                # Skip comments and empty lines
                if not line or line.startswith('#'):
                    continue

                # Count braces
                brace_count += line.count('{') - line.count('}')

                # Check menuentry syntax
                if line.startswith('menuentry'):
                    menuentry_count += 1
                    if not ('"' in line and '{' in line):
                        errors.append(f"Line {line_num}: Invalid menuentry syntax")

                # Check for common GRUB commands
                grub_commands = ['set', 'echo', 'linux', 'initrd', 'chainloader',
                               'loopback', 'search', 'insmod']

                for cmd in grub_commands:
                    if line.startswith(cmd + ' '):
                        # Basic command validation
                        if cmd in ['linux', 'initrd', 'chainloader'] and '(' not in line:
                            errors.append(f"Line {line_num}: {cmd} command may be missing device specification")

            # Check brace balance
            if brace_count != 0:
                errors.append("Unbalanced braces in configuration")

            # Check if we have at least one menuentry
            if menuentry_count == 0:
                errors.append("No menu entries found in configuration")

            is_valid = len(errors) == 0

            if is_valid:
                logger.info("GRUB configuration validation passed")
            else:
                logger.warning(f"GRUB configuration validation failed with {len(errors)} errors")

            return is_valid, errors

        except Exception as e:
            logger.error(f"Error validating GRUB configuration: {e}")
            return False, [f"Validation error: {e}"]
