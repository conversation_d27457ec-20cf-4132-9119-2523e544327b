"""
Platform-specific utilities
أدوات خاصة بكل منصة

Provides platform-specific functionality for Windows, Linux, and macOS
توفر وظائف خاصة بكل منصة لـ Windows و Linux و macOS
"""

import os
import sys
import platform
import subprocess
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class PlatformUtils:
    """Platform-specific utility functions"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.is_admin = self.check_admin_privileges()
        
        logger.info(f"Platform: {self.platform}")
        logger.info(f"Admin privileges: {self.is_admin}")
    
    def check_admin_privileges(self) -> bool:
        """
        Check if running with administrator/root privileges
        التحقق من تشغيل البرنامج بصلاحيات المدير/الجذر
        """
        try:
            if self.platform == "windows":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception as e:
            logger.warning(f"Could not check admin privileges: {e}")
            return False
    
    def request_admin_privileges(self) -> bool:
        """
        Request administrator privileges
        طلب صلاحيات المدير
        """
        try:
            if self.is_admin:
                return True
            
            if self.platform == "windows":
                return self._request_admin_windows()
            else:
                logger.info("Please run the application with sudo/root privileges")
                return False
                
        except Exception as e:
            logger.error(f"Error requesting admin privileges: {e}")
            return False
    
    def _request_admin_windows(self) -> bool:
        """Request admin privileges on Windows"""
        try:
            import ctypes
            import sys
            
            if ctypes.windll.shell32.IsUserAnAdmin():
                return True
            else:
                # Re-run the program with admin rights
                ctypes.windll.shell32.ShellExecuteW(
                    None, "runas", sys.executable, " ".join(sys.argv), None, 1
                )
                return False
                
        except Exception as e:
            logger.error(f"Error requesting Windows admin privileges: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, str]:
        """
        Get system information
        الحصول على معلومات النظام
        """
        try:
            info = {
                "platform": platform.system(),
                "platform_release": platform.release(),
                "platform_version": platform.version(),
                "architecture": platform.machine(),
                "processor": platform.processor(),
                "python_version": platform.python_version(),
                "hostname": platform.node()
            }
            
            # Add platform-specific info
            if self.platform == "windows":
                info.update(self._get_windows_info())
            elif self.platform == "linux":
                info.update(self._get_linux_info())
            elif self.platform == "darwin":
                info.update(self._get_macos_info())
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting system info: {e}")
            return {}
    
    def _get_windows_info(self) -> Dict[str, str]:
        """Get Windows-specific information"""
        info = {}
        try:
            # Get Windows version
            result = subprocess.run(
                ['wmic', 'os', 'get', 'Caption,Version', '/format:csv'],
                capture_output=True, text=True, check=True
            )
            
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 3:
                        info["windows_caption"] = parts[1].strip()
                        info["windows_version"] = parts[2].strip()
                        break
            
            # Get UEFI/BIOS info
            try:
                result = subprocess.run(
                    ['wmic', 'computersystem', 'get', 'BootupState', '/format:csv'],
                    capture_output=True, text=True, check=True
                )
                info["boot_state"] = result.stdout.strip()
            except:
                pass
                
        except Exception as e:
            logger.warning(f"Could not get Windows-specific info: {e}")
        
        return info
    
    def _get_linux_info(self) -> Dict[str, str]:
        """Get Linux-specific information"""
        info = {}
        try:
            # Get distribution info
            if Path("/etc/os-release").exists():
                with open("/etc/os-release", 'r') as f:
                    for line in f:
                        if line.startswith("PRETTY_NAME="):
                            info["distribution"] = line.split("=", 1)[1].strip().strip('"')
                            break
            
            # Get kernel version
            info["kernel_version"] = platform.release()
            
            # Check if UEFI
            if Path("/sys/firmware/efi").exists():
                info["firmware"] = "UEFI"
            else:
                info["firmware"] = "BIOS"
                
        except Exception as e:
            logger.warning(f"Could not get Linux-specific info: {e}")
        
        return info
    
    def _get_macos_info(self) -> Dict[str, str]:
        """Get macOS-specific information"""
        info = {}
        try:
            # Get macOS version
            result = subprocess.run(
                ['sw_vers', '-productVersion'],
                capture_output=True, text=True, check=True
            )
            info["macos_version"] = result.stdout.strip()
            
            # Get build version
            result = subprocess.run(
                ['sw_vers', '-buildVersion'],
                capture_output=True, text=True, check=True
            )
            info["macos_build"] = result.stdout.strip()
            
        except Exception as e:
            logger.warning(f"Could not get macOS-specific info: {e}")
        
        return info
    
    def get_available_drives(self) -> List[Dict[str, str]]:
        """
        Get list of available drives
        الحصول على قائمة الأقراص المتاحة
        """
        try:
            if self.platform == "windows":
                return self._get_windows_drives()
            elif self.platform == "linux":
                return self._get_linux_drives()
            elif self.platform == "darwin":
                return self._get_macos_drives()
            else:
                return []
                
        except Exception as e:
            logger.error(f"Error getting available drives: {e}")
            return []
    
    def _get_windows_drives(self) -> List[Dict[str, str]]:
        """Get Windows drives"""
        drives = []
        try:
            import string
            import win32api
            import win32file
            
            for letter in string.ascii_uppercase:
                drive = f"{letter}:\\"
                if os.path.exists(drive):
                    try:
                        drive_type = win32file.GetDriveType(drive)
                        if drive_type in [win32file.DRIVE_REMOVABLE, win32file.DRIVE_FIXED]:
                            free_bytes, total_bytes = win32api.GetDiskFreeSpace(drive)[:2]
                            label = win32api.GetVolumeInformation(drive)[0]
                            
                            drives.append({
                                "path": drive,
                                "label": label or f"Drive {letter}",
                                "size": total_bytes,
                                "free": free_bytes,
                                "type": "removable" if drive_type == win32file.DRIVE_REMOVABLE else "fixed"
                            })
                    except:
                        continue
                        
        except ImportError:
            logger.warning("pywin32 not available, using basic drive detection")
            # Fallback method
            for letter in "ABCDEFGHIJKLMNOPQRSTUVWXYZ":
                drive = f"{letter}:\\"
                if os.path.exists(drive):
                    drives.append({
                        "path": drive,
                        "label": f"Drive {letter}",
                        "size": 0,
                        "free": 0,
                        "type": "unknown"
                    })
        
        return drives
    
    def _get_linux_drives(self) -> List[Dict[str, str]]:
        """Get Linux drives"""
        drives = []
        try:
            # Use lsblk to get block devices
            result = subprocess.run(
                ['lsblk', '-J', '-o', 'NAME,SIZE,LABEL,FSTYPE,MOUNTPOINT,TYPE'],
                capture_output=True, text=True, check=True
            )
            
            import json
            data = json.loads(result.stdout)
            
            for device in data.get('blockdevices', []):
                if device.get('type') == 'disk':
                    drives.append({
                        "path": f"/dev/{device['name']}",
                        "label": device.get('label') or device['name'],
                        "size": self._parse_size_string(device.get('size', '0')),
                        "free": 0,  # Would need additional calculation
                        "type": "disk",
                        "mountpoint": device.get('mountpoint', '')
                    })
                    
        except Exception as e:
            logger.warning(f"Could not get Linux drives with lsblk: {e}")
        
        return drives
    
    def _get_macos_drives(self) -> List[Dict[str, str]]:
        """Get macOS drives"""
        drives = []
        try:
            # Use diskutil to get disk information
            result = subprocess.run(
                ['diskutil', 'list', '-plist'],
                capture_output=True, text=True, check=True
            )
            
            import plistlib
            data = plistlib.loads(result.stdout.encode())
            
            for disk in data.get('AllDisks', []):
                try:
                    # Get detailed info for each disk
                    info_result = subprocess.run(
                        ['diskutil', 'info', '-plist', disk],
                        capture_output=True, text=True, check=True
                    )
                    
                    disk_info = plistlib.loads(info_result.stdout.encode())
                    
                    drives.append({
                        "path": f"/dev/{disk}",
                        "label": disk_info.get('VolumeName', disk),
                        "size": disk_info.get('TotalSize', 0),
                        "free": disk_info.get('FreeSpace', 0),
                        "type": "removable" if disk_info.get('Removable', False) else "fixed",
                        "filesystem": disk_info.get('FilesystemType', '')
                    })
                    
                except:
                    continue
                    
        except Exception as e:
            logger.warning(f"Could not get macOS drives: {e}")
        
        return drives
    
    def _parse_size_string(self, size_str: str) -> int:
        """Parse size string like '8G', '512M' to bytes"""
        if not size_str:
            return 0
        
        size_str = size_str.upper().strip()
        multipliers = {
            'B': 1,
            'K': 1024,
            'M': 1024**2,
            'G': 1024**3,
            'T': 1024**4
        }
        
        for suffix, multiplier in multipliers.items():
            if size_str.endswith(suffix):
                try:
                    number = float(size_str[:-1])
                    return int(number * multiplier)
                except ValueError:
                    break
        
        try:
            return int(size_str)
        except ValueError:
            return 0
    
    def mount_device(self, device_path: str, mount_point: str) -> Tuple[bool, str]:
        """
        Mount a device
        تركيب جهاز
        """
        try:
            if self.platform == "windows":
                # Windows doesn't typically need manual mounting
                return True, "Device already accessible"
            elif self.platform == "linux":
                return self._mount_linux(device_path, mount_point)
            elif self.platform == "darwin":
                return self._mount_macos(device_path, mount_point)
            else:
                return False, f"Unsupported platform: {self.platform}"
                
        except Exception as e:
            return False, f"Error mounting device: {e}"
    
    def _mount_linux(self, device_path: str, mount_point: str) -> Tuple[bool, str]:
        """Mount device on Linux"""
        try:
            # Create mount point if it doesn't exist
            Path(mount_point).mkdir(parents=True, exist_ok=True)
            
            # Mount the device
            result = subprocess.run(
                ['sudo', 'mount', device_path, mount_point],
                capture_output=True, text=True, check=True
            )
            
            return True, f"Device mounted at {mount_point}"
            
        except subprocess.CalledProcessError as e:
            return False, f"Mount failed: {e.stderr}"
    
    def _mount_macos(self, device_path: str, mount_point: str) -> Tuple[bool, str]:
        """Mount device on macOS"""
        try:
            # Use diskutil to mount
            result = subprocess.run(
                ['diskutil', 'mount', device_path],
                capture_output=True, text=True, check=True
            )
            
            return True, f"Device mounted: {result.stdout.strip()}"
            
        except subprocess.CalledProcessError as e:
            return False, f"Mount failed: {e.stderr}"
    
    def unmount_device(self, device_path: str) -> Tuple[bool, str]:
        """
        Unmount a device
        إلغاء تركيب جهاز
        """
        try:
            if self.platform == "windows":
                # Windows doesn't typically need manual unmounting
                return True, "Device unmounted"
            elif self.platform == "linux":
                return self._unmount_linux(device_path)
            elif self.platform == "darwin":
                return self._unmount_macos(device_path)
            else:
                return False, f"Unsupported platform: {self.platform}"
                
        except Exception as e:
            return False, f"Error unmounting device: {e}"
    
    def _unmount_linux(self, device_path: str) -> Tuple[bool, str]:
        """Unmount device on Linux"""
        try:
            result = subprocess.run(
                ['sudo', 'umount', device_path],
                capture_output=True, text=True, check=True
            )
            
            return True, "Device unmounted successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Unmount failed: {e.stderr}"
    
    def _unmount_macos(self, device_path: str) -> Tuple[bool, str]:
        """Unmount device on macOS"""
        try:
            result = subprocess.run(
                ['diskutil', 'unmount', device_path],
                capture_output=True, text=True, check=True
            )
            
            return True, "Device unmounted successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Unmount failed: {e.stderr}"
    
    def get_temp_directory(self) -> Path:
        """Get platform-appropriate temporary directory"""
        if self.platform == "windows":
            return Path(os.environ.get("TEMP", "C:\\temp"))
        else:
            return Path("/tmp")
    
    def get_config_directory(self) -> Path:
        """Get platform-appropriate configuration directory"""
        if self.platform == "windows":
            return Path(os.environ.get("APPDATA", "")) / "OSLLTN"
        elif self.platform == "darwin":
            return Path.home() / "Library" / "Application Support" / "OSLLTN"
        else:  # Linux
            return Path.home() / ".config" / "oslltn"
    
    def open_file_manager(self, path: str) -> bool:
        """
        Open file manager at specified path
        فتح مدير الملفات في المسار المحدد
        """
        try:
            if self.platform == "windows":
                subprocess.run(['explorer', path], check=True)
            elif self.platform == "darwin":
                subprocess.run(['open', path], check=True)
            else:  # Linux
                subprocess.run(['xdg-open', path], check=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Error opening file manager: {e}")
            return False
