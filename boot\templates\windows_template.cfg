menuentry "{OS_NAME}" --class windows --class os {
    echo "Loading {OS_NAME}..."
    echo "جاري تحميل {OS_NAME}..."

    set iso_path="{ISO_PATH}"

    if [ -f "$iso_path" ]; then
        loopback loop "$iso_path"

        # Try different Windows boot methods
        if [ -f (loop)/sources/boot.wim ]; then
            # Windows Vista/7/8/10/11 with boot.wim
            echo "Booting Windows with boot.wim..."
            echo "إقلاع Windows مع boot.wim..."

            # Load wimboot
            if [ -f /EFI/OSLLTN/wimboot ]; then
                linux /EFI/OSLLTN/wimboot
                initrd (loop)/sources/boot.wim boot.wim
            else
                echo "wimboot not found!"
                echo "wimboot غير موجود!"
                sleep 3
            fi
        elif [ -f (loop)/bootmgr ]; then
            # Legacy Windows boot
            echo "Booting Windows with bootmgr..."
            echo "إقلاع Windows مع bootmgr..."
            chainloader (loop)/bootmgr
        else
            echo "No compatible Windows boot method found"
            echo "لم يتم العثور على طريقة إقلاع Windows متوافقة"
            sleep 3
        fi
    else
        echo "ISO file not found: $iso_path"
        echo "ملف ISO غير موجود: $iso_path"
        sleep 3
    fi
}