# OSLLTN Multi-Boot USB Creator
## نظام إنشاء فلاش USB متعدد الإقلاع

![Python](https://img.shields.io/badge/python-3.8+-blue.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

A powerful, cross-platform tool for creating multi-boot USB drives with support for Windows, Linux, and other operating systems. Similar to Ventoy but with additional features and a modern GUI.

أداة قوية ومتعددة المنصات لإنشاء فلاش USB متعدد الإقلاع مع دعم Windows و Linux وأنظمة التشغيل الأخرى. مشابه لـ Ventoy ولكن مع ميزات إضافية وواجهة رسومية حديثة.

## ✨ Features / الميزات

### Core Features / الميزات الأساسية
- **Multi-Platform Support** / **دعم متعدد المنصات**: Windows 10+, Linux, macOS
- **UEFI & Legacy BIOS** / **UEFI والـ BIOS التقليدي**: Full support for both boot modes
- **Multiple File Formats** / **تنسيقات ملفات متعددة**: ISO, WIM, IMG, ESD files
- **Automatic Detection** / **الكشف التلقائي**: Smart detection of OS type and architecture
- **Secure Boot Support** / **دعم Secure Boot**: Optional Secure Boot compatibility

### Advanced Features / الميزات المتقدمة
- **Checksum Verification** / **التحقق من Checksum**: MD5, SHA1, SHA256 support
- **QEMU Testing** / **اختبار QEMU**: Test your USB drive in virtual machine
- **Dynamic Boot Menu** / **قائمة إقلاع ديناميكية**: Automatically generated GRUB configuration
- **Dark Mode UI** / **واجهة الوضع الليلي**: Modern, user-friendly interface
- **Multi-Language** / **متعدد اللغات**: English and Arabic support

### Technical Features / الميزات التقنية
- **GRUB2 Bootloader** / **محمل الإقلاع GRUB2**: Reliable and flexible boot system
- **wimboot Integration** / **تكامل wimboot**: Windows PE and WIM file support
- **GPT Partitioning** / **تقسيم GPT**: Modern partition table support
- **Metadata Caching** / **تخزين البيانات الوصفية مؤقتاً**: Fast file scanning and management

## 🚀 Quick Start / البداية السريعة

### Prerequisites / المتطلبات المسبقة

- Python 3.8 or higher / Python 3.8 أو أحدث
- Administrator/root privileges / صلاحيات المدير/الجذر
- At least 4GB USB drive / فلاش USB بحجم 4 جيجابايت على الأقل

### Installation / التثبيت

#### Option 1: From Source / من المصدر
```bash
git clone https://github.com/oslltn/multiboot-usb-creator.git
cd multiboot-usb-creator
pip install -r requirements.txt
python main.py
```

#### Option 2: Using pip / باستخدام pip
```bash
pip install oslltn
oslltn-gui
```

#### Option 3: Standalone Executable / ملف تنفيذي مستقل
Download the latest release from [Releases](https://github.com/oslltn/multiboot-usb-creator/releases)

### Basic Usage / الاستخدام الأساسي

1. **Launch the application** / **تشغيل التطبيق**
   ```bash
   python main.py
   ```

2. **Select USB Device** / **اختيار جهاز USB**
   - Connect your USB drive / قم بتوصيل فلاش USB
   - Select it from the dropdown / اختره من القائمة المنسدلة

3. **Add ISO Files** / **إضافة ملفات ISO**
   - Click "Add Files" or "Scan Directory" / انقر على "إضافة ملفات" أو "فحص مجلد"
   - Select your ISO/WIM/IMG files / اختر ملفات ISO/WIM/IMG

4. **Prepare USB** / **تحضير USB**
   - Click "Prepare USB Device" / انقر على "تحضير جهاز USB"
   - Wait for partitioning to complete / انتظر حتى يكتمل التقسيم

5. **Install Bootloader** / **تثبيت محمل الإقلاع**
   - Click "Install Bootloader" / انقر على "تثبيت محمل الإقلاع"
   - Wait for installation / انتظر التثبيت

6. **Generate Boot Menu** / **إنشاء قائمة الإقلاع**
   - Click "Generate Boot Menu" / انقر على "إنشاء قائمة الإقلاع"
   - Your multi-boot USB is ready! / فلاش USB متعدد الإقلاع جاهز!

## 📋 Supported Operating Systems / أنظمة التشغيل المدعومة

### Windows
- Windows 7, 8, 8.1, 10, 11
- Windows Server 2008 R2, 2012, 2016, 2019, 2022
- Windows PE (WinPE)
- Windows Recovery Environment (WinRE)

### Linux Distributions / توزيعات Linux
- Ubuntu / أوبونتو
- Debian / ديبيان
- Fedora / فيدورا
- CentOS / سنت أو إس
- Arch Linux / آرش لينكس
- openSUSE / أوبن سوزي
- Mint / مينت
- Elementary OS / إليمنتري أو إس
- And many more... / والعديد غيرها...

### Other Systems / أنظمة أخرى
- FreeBSD
- OpenBSD
- Various rescue and utility disks / أقراص الإنقاذ والأدوات المختلفة

## 🛠️ Advanced Configuration / التكوين المتقدم

### Custom GRUB Configuration / تكوين GRUB مخصص
You can customize the GRUB boot menu by editing the templates in `boot/templates/`:

```bash
boot/templates/
├── main_template.cfg      # Main GRUB configuration
├── windows_template.cfg   # Windows boot entries
├── linux_template.cfg     # Linux boot entries
└── generic_template.cfg   # Generic boot entries
```

### Secure Boot Setup / إعداد Secure Boot
1. Generate signing keys:
   ```bash
   python -c "from utils.secure_boot import SecureBootManager; sbm = SecureBootManager(); sbm.generate_signing_keys(Path('keys'))"
   ```

2. Sign EFI binaries:
   ```bash
   python -c "from utils.secure_boot import SecureBootManager; sbm = SecureBootManager(); sbm.sign_efi_binary(Path('bootx64.efi'), Path('keys/cert.crt'), Path('keys/key.key'))"
   ```

### QEMU Testing / اختبار QEMU
Test your USB drive without rebooting:

```bash
python -c "
from utils.qemu_test import QEMUTester
tester = QEMUTester()
config = tester.create_test_configuration('/dev/sdb')  # Your USB device
tester.start_test(config)
"
```

## 🏗️ Project Structure / بنية المشروع

```
oslltn/
├── main.py                 # Application entry point / نقطة دخول التطبيق
├── requirements.txt        # Python dependencies / تبعيات Python
├── setup.py               # Installation script / سكريبت التثبيت
├── README.md              # This file / هذا الملف
├── core/                  # Core functionality / الوظائف الأساسية
│   ├── disk_manager.py    # USB device management / إدارة أجهزة USB
│   ├── bootloader.py      # GRUB2 installation / تثبيت GRUB2
│   ├── iso_manager.py     # ISO file handling / التعامل مع ملفات ISO
│   ├── grub_config.py     # GRUB configuration / تكوين GRUB
│   └── checksum.py        # Checksum utilities / أدوات Checksum
├── gui/                   # User interface / واجهة المستخدم
│   └── main_window.py     # Main application window / النافذة الرئيسية
├── utils/                 # Utility modules / وحدات الأدوات
│   ├── platform_utils.py  # Platform-specific code / كود خاص بالمنصة
│   ├── qemu_test.py       # QEMU testing / اختبار QEMU
│   └── secure_boot.py     # Secure Boot support / دعم Secure Boot
├── boot/                  # Boot files / ملفات الإقلاع
│   ├── grub/              # GRUB2 files / ملفات GRUB2
│   ├── wimboot/           # wimboot files / ملفات wimboot
│   └── templates/         # Configuration templates / قوالب التكوين
├── locales/               # Translation files / ملفات الترجمة
├── docs/                  # Documentation / التوثيق
└── tests/                 # Unit tests / اختبارات الوحدة
```

## 🧪 Testing / الاختبار

### Unit Tests / اختبارات الوحدة
```bash
pip install pytest pytest-qt
pytest tests/
```

### Integration Tests / اختبارات التكامل
```bash
pytest tests/integration/
```

### Manual Testing / الاختبار اليدوي
1. Test with different USB drives / اختبر مع فلاشات USB مختلفة
2. Test with various ISO files / اختبر مع ملفات ISO متنوعة
3. Test on different platforms / اختبر على منصات مختلفة
4. Test UEFI and BIOS boot modes / اختبر أوضاع إقلاع UEFI و BIOS

## 🤝 Contributing / المساهمة

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

نرحب بالمساهمات! يرجى مراجعة [دليل المساهمة](CONTRIBUTING.md) للتفاصيل.

### Development Setup / إعداد التطوير
```bash
git clone https://github.com/oslltn/multiboot-usb-creator.git
cd multiboot-usb-creator
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
pip install -e .
```

### Code Style / أسلوب الكود
We use Black for code formatting:
```bash
pip install black
black .
```

## 📚 Documentation / التوثيق

- [User Guide](docs/user-guide.md) / [دليل المستخدم](docs/user-guide-ar.md)
- [Developer Guide](docs/developer-guide.md) / [دليل المطور](docs/developer-guide-ar.md)
- [API Reference](docs/api-reference.md) / [مرجع API](docs/api-reference-ar.md)
- [Troubleshooting](docs/troubleshooting.md) / [استكشاف الأخطاء](docs/troubleshooting-ar.md)

## 🐛 Known Issues / المشاكل المعروفة

- **Windows**: Requires administrator privileges / يتطلب صلاحيات المدير
- **Linux**: May require sudo for disk operations / قد يتطلب sudo لعمليات القرص
- **macOS**: Some USB devices may not be detected / قد لا يتم اكتشاف بعض أجهزة USB

## 🔧 Troubleshooting / استكشاف الأخطاء

### Common Issues / المشاكل الشائعة

#### USB Device Not Detected / لا يتم اكتشاف جهاز USB
- Ensure USB is properly connected / تأكد من توصيل USB بشكل صحيح
- Try a different USB port / جرب منفذ USB مختلف
- Run as administrator/root / شغل بصلاحيات المدير/الجذر

#### Boot Menu Not Appearing / قائمة الإقلاع لا تظهر
- Check BIOS/UEFI boot order / تحقق من ترتيب الإقلاع في BIOS/UEFI
- Disable Secure Boot temporarily / عطل Secure Boot مؤقتاً
- Try different boot mode (UEFI/Legacy) / جرب وضع إقلاع مختلف

#### ISO Files Not Booting / ملفات ISO لا تقلع
- Verify ISO file integrity / تحقق من سلامة ملف ISO
- Check if ISO is bootable / تحقق من أن ISO قابل للإقلاع
- Try regenerating boot configuration / جرب إعادة إنشاء تكوين الإقلاع

## 📄 License / الترخيص

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 Acknowledgments / الشكر والتقدير

- **GRUB2** team for the excellent bootloader / فريق GRUB2 لمحمل الإقلاع الممتاز
- **wimboot** project for Windows PE support / مشروع wimboot لدعم Windows PE
- **Ventoy** project for inspiration / مشروع Ventoy للإلهام
- **PyQt6** for the GUI framework / PyQt6 لإطار عمل الواجهة الرسومية
- All contributors and testers / جميع المساهمين والمختبرين

## 📞 Support / الدعم

- **GitHub Issues**: [Report bugs](https://github.com/oslltn/multiboot-usb-creator/issues)
- **Discussions**: [Community discussions](https://github.com/oslltn/multiboot-usb-creator/discussions)
- **Email**: <EMAIL>
- **Documentation**: [Full documentation](https://oslltn.readthedocs.io/)

## 🗺️ Roadmap / خارطة الطريق

### Version 1.1 / الإصدار 1.1
- [ ] Persistence support for Linux distributions / دعم الاستمرارية لتوزيعات Linux
- [ ] Custom themes for GRUB / ثيمات مخصصة لـ GRUB
- [ ] Automatic ISO download / تحميل ISO تلقائي
- [ ] Plugin system / نظام الإضافات

### Version 1.2 / الإصدار 1.2
- [ ] Web interface / واجهة ويب
- [ ] Remote management / الإدارة عن بُعد
- [ ] Cloud storage integration / تكامل التخزين السحابي
- [ ] Advanced partitioning options / خيارات تقسيم متقدمة

### Version 2.0 / الإصدار 2.0
- [ ] Complete rewrite in Rust / إعادة كتابة كاملة بـ Rust
- [ ] Native mobile apps / تطبيقات محمولة أصلية
- [ ] Enterprise features / ميزات المؤسسات
- [ ] AI-powered OS detection / كشف نظام التشغيل بالذكاء الاصطناعي

---

**Made with ❤️ by the OSLLTN team** / **صُنع بـ ❤️ من فريق OSLLTN**
