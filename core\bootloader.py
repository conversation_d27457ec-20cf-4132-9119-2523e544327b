"""
Bootloader Manager Module
وحدة إدارة محمل الإقلاع

Handles GRUB2 and wimboot installation and configuration
تتعامل مع تثبيت وتكوين GRUB2 و wimboot
"""

import os
import sys
import logging
import subprocess
import platform
import shutil
from typing import Dict, Tuple, Optional
from pathlib import Path
import urllib.request
import zipfile

logger = logging.getLogger(__name__)

class BootloaderManager:
    """Manages bootloader installation and configuration"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.project_root = Path(__file__).parent.parent
        self.boot_files_dir = self.project_root / "boot"
        self.grub_dir = self.boot_files_dir / "grub"
        self.wimboot_dir = self.boot_files_dir / "wimboot"
        
        # Create directories if they don't exist
        self.boot_files_dir.mkdir(exist_ok=True)
        self.grub_dir.mkdir(exist_ok=True)
        self.wimboot_dir.mkdir(exist_ok=True)
        
        logger.info(f"Initialized BootloaderManager for platform: {self.platform}")
    
    def download_boot_files(self) -> Tuple[bool, str]:
        """
        Download necessary boot files (GRUB2, wimboot)
        تحميل ملفات الإقلاع الضرورية
        """
        try:
            logger.info("Downloading boot files...")
            
            # Download GRUB2 files
            grub_success, grub_msg = self._download_grub_files()
            if not grub_success:
                return False, f"GRUB download failed: {grub_msg}"
            
            # Download wimboot files
            wimboot_success, wimboot_msg = self._download_wimboot_files()
            if not wimboot_success:
                return False, f"wimboot download failed: {wimboot_msg}"
            
            logger.info("All boot files downloaded successfully")
            return True, "Boot files downloaded successfully"
            
        except Exception as e:
            error_msg = f"Error downloading boot files: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _download_grub_files(self) -> Tuple[bool, str]:
        """Download GRUB2 files"""
        try:
            # GRUB2 EFI files URLs
            grub_urls = {
                "grubx64.efi": "https://github.com/pbatard/rufus/raw/master/res/grub/grubx64.efi",
                "grubia32.efi": "https://github.com/pbatard/rufus/raw/master/res/grub/grubia32.efi",
                "grub.cfg": None  # We'll create this ourselves
            }
            
            for filename, url in grub_urls.items():
                if url:
                    file_path = self.grub_dir / filename
                    if not file_path.exists():
                        logger.info(f"Downloading {filename}...")
                        urllib.request.urlretrieve(url, file_path)
                        logger.info(f"Downloaded {filename}")
            
            # Create basic grub.cfg template
            self._create_grub_template()
            
            return True, "GRUB files downloaded successfully"
            
        except Exception as e:
            return False, str(e)
    
    def _download_wimboot_files(self) -> Tuple[bool, str]:
        """Download wimboot files"""
        try:
            # wimboot URLs
            wimboot_urls = {
                "wimboot": "https://github.com/ipxe/wimboot/releases/latest/download/wimboot",
                "wimboot.exe": "https://github.com/ipxe/wimboot/releases/latest/download/wimboot.exe"
            }
            
            for filename, url in wimboot_urls.items():
                file_path = self.wimboot_dir / filename
                if not file_path.exists():
                    logger.info(f"Downloading {filename}...")
                    urllib.request.urlretrieve(url, file_path)
                    logger.info(f"Downloaded {filename}")
            
            return True, "wimboot files downloaded successfully"
            
        except Exception as e:
            return False, str(e)
    
    def _create_grub_template(self):
        """Create basic GRUB configuration template"""
        grub_template = '''# OSLLTN Multi-Boot USB Configuration
# تكوين فلاش USB متعدد الإقلاع

set timeout=10
set default=0

# Set theme and appearance
set color_normal=white/black
set color_highlight=black/light-gray

# Main menu
menuentry "OSLLTN Multi-Boot Menu" {
    echo "Loading OSLLTN Multi-Boot system..."
    echo "جاري تحميل نظام الإقلاع المتعدد..."
}

# Separator
menuentry "--- Boot Options ---" {
    true
}

# This will be dynamically populated by the application
# سيتم ملء هذا القسم ديناميكياً بواسطة التطبيق

# Advanced options
submenu "Advanced Options" {
    menuentry "Memory Test (memtest86+)" {
        linux16 /boot/memtest86+.bin
    }
    
    menuentry "Hardware Detection Tool" {
        linux /boot/hdt.c32
    }
    
    menuentry "System Information" {
        echo "System: $grub_platform"
        echo "CPU: $grub_cpu"
        sleep 3
    }
}

# Utilities
submenu "Utilities" {
    menuentry "Reboot" {
        reboot
    }
    
    menuentry "Shutdown" {
        halt
    }
    
    menuentry "UEFI Firmware Settings" {
        fwsetup
    }
}
'''
        
        grub_cfg_path = self.grub_dir / "grub.cfg"
        with open(grub_cfg_path, 'w', encoding='utf-8') as f:
            f.write(grub_template)
        
        logger.info("Created GRUB configuration template")
    
    def install_bootloader(self, esp_partition: str) -> Tuple[bool, str]:
        """
        Install GRUB2 bootloader to ESP partition
        تثبيت محمل الإقلاع GRUB2 على قسم ESP
        
        Args:
            esp_partition: Path to ESP partition
            
        Returns:
            Tuple of (success, message)
        """
        try:
            logger.info(f"Installing bootloader to {esp_partition}")
            
            if self.platform == "windows":
                return self._install_bootloader_windows(esp_partition)
            elif self.platform == "linux":
                return self._install_bootloader_linux(esp_partition)
            elif self.platform == "darwin":
                return self._install_bootloader_macos(esp_partition)
            else:
                return False, f"Unsupported platform: {self.platform}"
                
        except Exception as e:
            error_msg = f"Error installing bootloader: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _install_bootloader_windows(self, esp_partition: str) -> Tuple[bool, str]:
        """Install bootloader on Windows"""
        try:
            # Mount ESP partition if not already mounted
            esp_path = Path(esp_partition)
            if not esp_path.exists():
                return False, f"ESP partition not accessible: {esp_partition}"
            
            # Create EFI directory structure
            efi_dir = esp_path / "EFI"
            boot_dir = efi_dir / "BOOT"
            oslltn_dir = efi_dir / "OSLLTN"
            
            efi_dir.mkdir(exist_ok=True)
            boot_dir.mkdir(exist_ok=True)
            oslltn_dir.mkdir(exist_ok=True)
            
            # Copy GRUB EFI files
            grub_efi_source = self.grub_dir / "grubx64.efi"
            if grub_efi_source.exists():
                # Copy as bootx64.efi for default boot
                shutil.copy2(grub_efi_source, boot_dir / "bootx64.efi")
                # Copy as grubx64.efi for OSLLTN
                shutil.copy2(grub_efi_source, oslltn_dir / "grubx64.efi")
            
            # Copy GRUB configuration
            grub_cfg_source = self.grub_dir / "grub.cfg"
            if grub_cfg_source.exists():
                shutil.copy2(grub_cfg_source, oslltn_dir / "grub.cfg")
            
            # Copy wimboot files
            wimboot_source = self.wimboot_dir / "wimboot"
            if wimboot_source.exists():
                shutil.copy2(wimboot_source, oslltn_dir / "wimboot")
            
            logger.info("Bootloader installed successfully on Windows")
            return True, "Bootloader installed successfully"
            
        except Exception as e:
            return False, f"Error installing bootloader on Windows: {e}"
    
    def _install_bootloader_linux(self, esp_partition: str) -> Tuple[bool, str]:
        """Install bootloader on Linux"""
        try:
            # Create temporary mount point
            mount_point = Path("/tmp/oslltn_esp")
            mount_point.mkdir(exist_ok=True)
            
            # Mount ESP partition
            subprocess.run(['sudo', 'mount', esp_partition, str(mount_point)], 
                         check=True, capture_output=True)
            
            try:
                # Create EFI directory structure
                efi_dir = mount_point / "EFI"
                boot_dir = efi_dir / "BOOT"
                oslltn_dir = efi_dir / "OSLLTN"
                
                subprocess.run(['sudo', 'mkdir', '-p', str(boot_dir)], check=True)
                subprocess.run(['sudo', 'mkdir', '-p', str(oslltn_dir)], check=True)
                
                # Copy GRUB EFI files
                grub_efi_source = self.grub_dir / "grubx64.efi"
                if grub_efi_source.exists():
                    subprocess.run(['sudo', 'cp', str(grub_efi_source), 
                                  str(boot_dir / "bootx64.efi")], check=True)
                    subprocess.run(['sudo', 'cp', str(grub_efi_source), 
                                  str(oslltn_dir / "grubx64.efi")], check=True)
                
                # Copy GRUB configuration
                grub_cfg_source = self.grub_dir / "grub.cfg"
                if grub_cfg_source.exists():
                    subprocess.run(['sudo', 'cp', str(grub_cfg_source), 
                                  str(oslltn_dir / "grub.cfg")], check=True)
                
                # Copy wimboot files
                wimboot_source = self.wimboot_dir / "wimboot"
                if wimboot_source.exists():
                    subprocess.run(['sudo', 'cp', str(wimboot_source), 
                                  str(oslltn_dir / "wimboot")], check=True)
                
                # Set proper permissions
                subprocess.run(['sudo', 'chmod', '-R', '755', str(efi_dir)], check=True)
                
            finally:
                # Unmount ESP partition
                subprocess.run(['sudo', 'umount', str(mount_point)], 
                             capture_output=True, check=False)
                mount_point.rmdir()
            
            logger.info("Bootloader installed successfully on Linux")
            return True, "Bootloader installed successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Command failed: {e.stderr.decode()}"
        except Exception as e:
            return False, f"Error installing bootloader on Linux: {e}"
    
    def _install_bootloader_macos(self, esp_partition: str) -> Tuple[bool, str]:
        """Install bootloader on macOS"""
        try:
            # Create temporary mount point
            mount_point = Path("/tmp/oslltn_esp")
            mount_point.mkdir(exist_ok=True)
            
            # Mount ESP partition
            subprocess.run(['sudo', 'mount', '-t', 'msdos', esp_partition, str(mount_point)], 
                         check=True, capture_output=True)
            
            try:
                # Create EFI directory structure
                efi_dir = mount_point / "EFI"
                boot_dir = efi_dir / "BOOT"
                oslltn_dir = efi_dir / "OSLLTN"
                
                efi_dir.mkdir(exist_ok=True)
                boot_dir.mkdir(exist_ok=True)
                oslltn_dir.mkdir(exist_ok=True)
                
                # Copy GRUB EFI files
                grub_efi_source = self.grub_dir / "grubx64.efi"
                if grub_efi_source.exists():
                    shutil.copy2(grub_efi_source, boot_dir / "bootx64.efi")
                    shutil.copy2(grub_efi_source, oslltn_dir / "grubx64.efi")
                
                # Copy GRUB configuration
                grub_cfg_source = self.grub_dir / "grub.cfg"
                if grub_cfg_source.exists():
                    shutil.copy2(grub_cfg_source, oslltn_dir / "grub.cfg")
                
                # Copy wimboot files
                wimboot_source = self.wimboot_dir / "wimboot"
                if wimboot_source.exists():
                    shutil.copy2(wimboot_source, oslltn_dir / "wimboot")
                
            finally:
                # Unmount ESP partition
                subprocess.run(['sudo', 'umount', str(mount_point)], 
                             capture_output=True, check=False)
                mount_point.rmdir()
            
            logger.info("Bootloader installed successfully on macOS")
            return True, "Bootloader installed successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Command failed: {e.stderr.decode()}"
        except Exception as e:
            return False, f"Error installing bootloader on macOS: {e}"
    
    def update_bootloader(self, esp_partition: str) -> Tuple[bool, str]:
        """
        Update bootloader configuration
        تحديث تكوين محمل الإقلاع
        """
        try:
            logger.info("Updating bootloader configuration...")
            
            # Re-download latest boot files
            download_success, download_msg = self.download_boot_files()
            if not download_success:
                return False, f"Failed to download updated boot files: {download_msg}"
            
            # Reinstall bootloader
            install_success, install_msg = self.install_bootloader(esp_partition)
            if not install_success:
                return False, f"Failed to reinstall bootloader: {install_msg}"
            
            logger.info("Bootloader updated successfully")
            return True, "Bootloader updated successfully"
            
        except Exception as e:
            error_msg = f"Error updating bootloader: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def verify_bootloader_installation(self, esp_partition: str) -> Tuple[bool, str]:
        """
        Verify that bootloader is properly installed
        التحقق من تثبيت محمل الإقلاع بشكل صحيح
        """
        try:
            logger.info("Verifying bootloader installation...")
            
            if self.platform == "windows":
                esp_path = Path(esp_partition)
                required_files = [
                    esp_path / "EFI" / "BOOT" / "bootx64.efi",
                    esp_path / "EFI" / "OSLLTN" / "grubx64.efi",
                    esp_path / "EFI" / "OSLLTN" / "grub.cfg"
                ]
            else:
                # For Linux/macOS, we need to mount and check
                return True, "Bootloader verification not implemented for this platform"
            
            missing_files = []
            for file_path in required_files:
                if not file_path.exists():
                    missing_files.append(str(file_path))
            
            if missing_files:
                return False, f"Missing bootloader files: {', '.join(missing_files)}"
            
            logger.info("Bootloader installation verified successfully")
            return True, "Bootloader installation verified"
            
        except Exception as e:
            error_msg = f"Error verifying bootloader installation: {e}"
            logger.error(error_msg)
            return False, error_msg
