#!/usr/bin/env python3
"""
Setup script for OSLLTN Multi-Boot USB Creator
سكريبت الإعداد لنظام إنشاء فلاش USB متعدد الإقلاع
"""

from setuptools import setup, find_packages
from pathlib import Path
import sys

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = ""
if readme_file.exists():
    with open(readme_file, 'r', encoding='utf-8') as f:
        long_description = f.read()

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]

# Platform-specific requirements
if sys.platform == "win32":
    requirements.append("pywin32>=306")

setup(
    name="oslltn",
    version="1.0.0",
    author="OSLLTN Project",
    author_email="<EMAIL>",
    description="Multi-Boot USB Creator - نظام إنشاء فلاش USB متعدد الإقلاع",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/oslltn/multiboot-usb-creator",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "Intended Audience :: System Administrators",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: System :: Boot",
        "Topic :: System :: Systems Administration",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "oslltn=main:main",
            "oslltn-gui=main:main",
        ],
        "gui_scripts": [
            "oslltn-gui=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "boot": ["grub/*", "wimboot/*", "templates/*"],
        "locales": ["*.qm", "*.ts"],
        "docs": ["*.md", "*.rst"],
    },
    zip_safe=False,
    keywords=[
        "usb", "boot", "multiboot", "grub", "uefi", "bios",
        "iso", "wim", "windows", "linux", "ventoy"
    ],
    project_urls={
        "Bug Reports": "https://github.com/oslltn/multiboot-usb-creator/issues",
        "Source": "https://github.com/oslltn/multiboot-usb-creator",
        "Documentation": "https://oslltn.readthedocs.io/",
    },
)
