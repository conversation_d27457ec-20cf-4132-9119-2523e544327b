"""
ISO Manager Module
وحدة إدارة ملفات ISO

Handles ISO/WIM/IMG file detection, metadata extraction, and management
تتعامل مع اكتشاف ملفات ISO/WIM/IMG واستخراج البيانات الوصفية وإدارتها
"""

import os
import sys
import logging
import struct
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import hashlib
import json
from dataclasses import dataclass, asdict
from datetime import datetime

try:
    import pycdlib
except ImportError:
    pycdlib = None

logger = logging.getLogger(__name__)

@dataclass
class ISOMetadata:
    """Metadata for an ISO/WIM/IMG file"""
    filename: str
    filepath: str
    size: int
    file_type: str  # 'iso', 'wim', 'img'
    os_type: str    # 'windows', 'linux', 'other'
    os_name: str
    os_version: str
    architecture: str  # 'x86', 'x64', 'arm64'
    bootable: bool
    checksum_md5: Optional[str] = None
    checksum_sha256: Optional[str] = None
    creation_date: Optional[str] = None
    last_modified: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'ISOMetadata':
        """Create from dictionary"""
        return cls(**data)

class ISOManager:
    """Manages ISO/WIM/IMG files and their metadata"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.cache_file = self.project_root / "cache" / "iso_metadata.json"
        self.cache_file.parent.mkdir(exist_ok=True)
        self.metadata_cache = self._load_cache()
        
        logger.info("Initialized ISOManager")
    
    def _load_cache(self) -> Dict[str, ISOMetadata]:
        """Load metadata cache from file"""
        try:
            if self.cache_file.exists():
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return {k: ISOMetadata.from_dict(v) for k, v in data.items()}
        except Exception as e:
            logger.warning(f"Could not load metadata cache: {e}")
        
        return {}
    
    def _save_cache(self):
        """Save metadata cache to file"""
        try:
            data = {k: v.to_dict() for k, v in self.metadata_cache.items()}
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"Could not save metadata cache: {e}")
    
    def scan_directory(self, directory: Path) -> List[ISOMetadata]:
        """
        Scan directory for ISO/WIM/IMG files
        فحص المجلد للبحث عن ملفات ISO/WIM/IMG
        
        Args:
            directory: Directory to scan
            
        Returns:
            List of ISOMetadata objects
        """
        try:
            logger.info(f"Scanning directory: {directory}")
            
            if not directory.exists():
                logger.warning(f"Directory does not exist: {directory}")
                return []
            
            supported_extensions = {'.iso', '.wim', '.img', '.esd'}
            found_files = []
            
            for file_path in directory.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    logger.info(f"Found file: {file_path}")
                    
                    # Check if we have cached metadata
                    cache_key = str(file_path)
                    if (cache_key in self.metadata_cache and 
                        self._is_cache_valid(file_path, self.metadata_cache[cache_key])):
                        found_files.append(self.metadata_cache[cache_key])
                    else:
                        # Extract metadata
                        metadata = self._extract_metadata(file_path)
                        if metadata:
                            found_files.append(metadata)
                            self.metadata_cache[cache_key] = metadata
            
            # Save updated cache
            self._save_cache()
            
            logger.info(f"Found {len(found_files)} bootable files")
            return found_files
            
        except Exception as e:
            logger.error(f"Error scanning directory: {e}")
            return []
    
    def _is_cache_valid(self, file_path: Path, metadata: ISOMetadata) -> bool:
        """Check if cached metadata is still valid"""
        try:
            stat = file_path.stat()
            return (stat.st_size == metadata.size and 
                    datetime.fromtimestamp(stat.st_mtime).isoformat() == metadata.last_modified)
        except:
            return False
    
    def _extract_metadata(self, file_path: Path) -> Optional[ISOMetadata]:
        """
        Extract metadata from ISO/WIM/IMG file
        استخراج البيانات الوصفية من ملف ISO/WIM/IMG
        """
        try:
            logger.info(f"Extracting metadata from: {file_path}")
            
            stat = file_path.stat()
            file_type = self._detect_file_type(file_path)
            
            metadata = ISOMetadata(
                filename=file_path.name,
                filepath=str(file_path),
                size=stat.st_size,
                file_type=file_type,
                os_type='unknown',
                os_name='Unknown',
                os_version='Unknown',
                architecture='unknown',
                bootable=False,
                creation_date=datetime.fromtimestamp(stat.st_ctime).isoformat(),
                last_modified=datetime.fromtimestamp(stat.st_mtime).isoformat()
            )
            
            # Extract specific metadata based on file type
            if file_type == 'iso':
                self._extract_iso_metadata(file_path, metadata)
            elif file_type == 'wim':
                self._extract_wim_metadata(file_path, metadata)
            elif file_type == 'img':
                self._extract_img_metadata(file_path, metadata)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata from {file_path}: {e}")
            return None
    
    def _detect_file_type(self, file_path: Path) -> str:
        """Detect file type based on extension and magic bytes"""
        extension = file_path.suffix.lower()
        
        if extension == '.iso':
            return 'iso'
        elif extension in ['.wim', '.esd']:
            return 'wim'
        elif extension == '.img':
            return 'img'
        else:
            # Try to detect by magic bytes
            try:
                with open(file_path, 'rb') as f:
                    magic = f.read(8)
                    
                    # ISO 9660 magic
                    if b'CD001' in magic:
                        return 'iso'
                    # WIM magic
                    elif magic.startswith(b'MSWIM\x00\x00\x00'):
                        return 'wim'
                    # FAT/NTFS magic (for IMG files)
                    elif magic[3:8] == b'NTFS ' or magic[54:62] == b'FAT32   ':
                        return 'img'
            except:
                pass
        
        return 'unknown'
    
    def _extract_iso_metadata(self, file_path: Path, metadata: ISOMetadata):
        """Extract metadata from ISO file"""
        try:
            if pycdlib is None:
                logger.warning("pycdlib not available, using basic ISO detection")
                self._extract_iso_metadata_basic(file_path, metadata)
                return
            
            # Use pycdlib for detailed ISO analysis
            iso = pycdlib.PyCdlib()
            iso.open(str(file_path))
            
            try:
                # Check if bootable
                if iso.eltorito_boot_catalog is not None:
                    metadata.bootable = True
                
                # Get volume identifier
                if hasattr(iso, 'pvd') and iso.pvd:
                    volume_id = iso.pvd.volume_identifier.decode('utf-8', errors='ignore').strip()
                    if volume_id:
                        metadata.os_name = volume_id
                
                # Detect OS type and architecture from common patterns
                self._detect_os_from_iso_content(iso, metadata)
                
            finally:
                iso.close()
                
        except Exception as e:
            logger.warning(f"Error extracting ISO metadata with pycdlib: {e}")
            self._extract_iso_metadata_basic(file_path, metadata)
    
    def _extract_iso_metadata_basic(self, file_path: Path, metadata: ISOMetadata):
        """Basic ISO metadata extraction without pycdlib"""
        try:
            with open(file_path, 'rb') as f:
                # Read primary volume descriptor
                f.seek(32768)  # PVD is at sector 16 (16 * 2048)
                pvd = f.read(2048)
                
                if pvd[1:6] == b'CD001':
                    # Extract volume identifier
                    volume_id = pvd[40:72].decode('utf-8', errors='ignore').strip()
                    if volume_id:
                        metadata.os_name = volume_id
                    
                    # Check for El Torito boot record
                    f.seek(34816)  # Boot record volume descriptor
                    boot_record = f.read(2048)
                    if boot_record[1:6] == b'CD001' and boot_record[0] == 0:
                        metadata.bootable = True
                
                # Detect OS type from filename patterns
                filename_lower = file_path.name.lower()
                if 'windows' in filename_lower or 'win' in filename_lower:
                    metadata.os_type = 'windows'
                    if 'x64' in filename_lower or 'amd64' in filename_lower:
                        metadata.architecture = 'x64'
                    elif 'x86' in filename_lower or 'i386' in filename_lower:
                        metadata.architecture = 'x86'
                elif any(distro in filename_lower for distro in ['ubuntu', 'debian', 'fedora', 'centos', 'arch', 'linux']):
                    metadata.os_type = 'linux'
                    if 'amd64' in filename_lower or 'x86_64' in filename_lower:
                        metadata.architecture = 'x64'
                    elif 'i386' in filename_lower or 'i686' in filename_lower:
                        metadata.architecture = 'x86'
                
        except Exception as e:
            logger.warning(f"Error in basic ISO metadata extraction: {e}")
    
    def _detect_os_from_iso_content(self, iso, metadata: ISOMetadata):
        """Detect OS type from ISO content"""
        try:
            # Look for common Windows files
            windows_files = ['/SOURCES/BOOT.WIM', '/SOURCES/INSTALL.WIM', '/BOOTMGR', '/SETUP.EXE']
            linux_files = ['/CASPER/', '/LIVE/', '/ISOLINUX/', '/SYSLINUX/']
            
            for file_path in windows_files:
                try:
                    if iso.get_file_from_iso(file_path):
                        metadata.os_type = 'windows'
                        break
                except:
                    continue
            
            if metadata.os_type == 'unknown':
                for dir_path in linux_files:
                    try:
                        if iso.get_file_from_iso(dir_path):
                            metadata.os_type = 'linux'
                            break
                    except:
                        continue
                        
        except Exception as e:
            logger.warning(f"Error detecting OS from ISO content: {e}")
    
    def _extract_wim_metadata(self, file_path: Path, metadata: ISOMetadata):
        """Extract metadata from WIM file"""
        try:
            with open(file_path, 'rb') as f:
                # Read WIM header
                header = f.read(208)  # WIM header is 208 bytes
                
                if header[:8] == b'MSWIM\x00\x00\x00':
                    # Parse WIM header
                    flags = struct.unpack('<I', header[12:16])[0]
                    metadata.bootable = bool(flags & 0x00020000)  # WIM_HDR_FLAG_BOOT_METADATA
                    
                    # WIM files are typically Windows
                    metadata.os_type = 'windows'
                    metadata.os_name = 'Windows (WIM)'
                    
                    # Try to detect architecture from filename
                    filename_lower = file_path.name.lower()
                    if 'x64' in filename_lower or 'amd64' in filename_lower:
                        metadata.architecture = 'x64'
                    elif 'x86' in filename_lower or 'i386' in filename_lower:
                        metadata.architecture = 'x86'
                    elif 'arm64' in filename_lower:
                        metadata.architecture = 'arm64'
                        
        except Exception as e:
            logger.warning(f"Error extracting WIM metadata: {e}")
    
    def _extract_img_metadata(self, file_path: Path, metadata: ISOMetadata):
        """Extract metadata from IMG file"""
        try:
            with open(file_path, 'rb') as f:
                # Read boot sector
                boot_sector = f.read(512)
                
                # Check for bootable signature
                if boot_sector[510:512] == b'\x55\xAA':
                    metadata.bootable = True
                
                # Try to detect filesystem
                if boot_sector[3:8] == b'NTFS ':
                    metadata.os_type = 'windows'
                    metadata.os_name = 'Windows (IMG)'
                elif boot_sector[54:62] == b'FAT32   ':
                    # Could be Windows or Linux
                    filename_lower = file_path.name.lower()
                    if 'windows' in filename_lower or 'win' in filename_lower:
                        metadata.os_type = 'windows'
                        metadata.os_name = 'Windows (IMG)'
                    else:
                        metadata.os_type = 'unknown'
                        metadata.os_name = 'Disk Image'
                        
        except Exception as e:
            logger.warning(f"Error extracting IMG metadata: {e}")
    
    def calculate_checksum(self, file_path: Path, algorithm: str = 'md5') -> Optional[str]:
        """
        Calculate file checksum
        حساب checksum للملف
        
        Args:
            file_path: Path to file
            algorithm: 'md5' or 'sha256'
            
        Returns:
            Checksum string or None if error
        """
        try:
            logger.info(f"Calculating {algorithm} checksum for: {file_path}")
            
            if algorithm == 'md5':
                hasher = hashlib.md5()
            elif algorithm == 'sha256':
                hasher = hashlib.sha256()
            else:
                raise ValueError(f"Unsupported algorithm: {algorithm}")
            
            with open(file_path, 'rb') as f:
                # Read in chunks to handle large files
                for chunk in iter(lambda: f.read(8192), b""):
                    hasher.update(chunk)
            
            checksum = hasher.hexdigest()
            logger.info(f"{algorithm.upper()} checksum: {checksum}")
            return checksum
            
        except Exception as e:
            logger.error(f"Error calculating checksum: {e}")
            return None
    
    def update_metadata_checksums(self, metadata: ISOMetadata) -> bool:
        """
        Update metadata with calculated checksums
        تحديث البيانات الوصفية مع checksums المحسوبة
        """
        try:
            file_path = Path(metadata.filepath)
            if not file_path.exists():
                return False
            
            # Calculate checksums
            metadata.checksum_md5 = self.calculate_checksum(file_path, 'md5')
            metadata.checksum_sha256 = self.calculate_checksum(file_path, 'sha256')
            
            # Update cache
            self.metadata_cache[str(file_path)] = metadata
            self._save_cache()
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating metadata checksums: {e}")
            return False
    
    def verify_file_integrity(self, metadata: ISOMetadata) -> Tuple[bool, str]:
        """
        Verify file integrity using stored checksums
        التحقق من سلامة الملف باستخدام checksums المحفوظة
        """
        try:
            file_path = Path(metadata.filepath)
            if not file_path.exists():
                return False, "File not found"
            
            if not metadata.checksum_md5 and not metadata.checksum_sha256:
                return False, "No checksums available for verification"
            
            # Verify MD5 if available
            if metadata.checksum_md5:
                current_md5 = self.calculate_checksum(file_path, 'md5')
                if current_md5 != metadata.checksum_md5:
                    return False, "MD5 checksum mismatch"
            
            # Verify SHA256 if available
            if metadata.checksum_sha256:
                current_sha256 = self.calculate_checksum(file_path, 'sha256')
                if current_sha256 != metadata.checksum_sha256:
                    return False, "SHA256 checksum mismatch"
            
            return True, "File integrity verified"
            
        except Exception as e:
            return False, f"Error verifying file integrity: {e}"
    
    def get_bootable_files(self, directory: Path) -> List[ISOMetadata]:
        """
        Get only bootable files from directory
        الحصول على الملفات القابلة للإقلاع فقط من المجلد
        """
        all_files = self.scan_directory(directory)
        return [f for f in all_files if f.bootable]
    
    def clear_cache(self):
        """Clear metadata cache"""
        self.metadata_cache.clear()
        if self.cache_file.exists():
            self.cache_file.unlink()
        logger.info("Metadata cache cleared")
