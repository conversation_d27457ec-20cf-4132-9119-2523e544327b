"""
Secure Boot Support Module
وحدة دعم Secure Boot

Provides functionality for Secure Boot support and EFI signing
توفر وظائف لدعم Secure Boot وتوقيع EFI
"""

import os
import sys
import logging
import subprocess
import platform
from typing import Optional, Tuple, Dict, List
from pathlib import Path
import hashlib
import tempfile

logger = logging.getLogger(__name__)

class SecureBootManager:
    """Manages Secure Boot functionality"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.openssl_path = self.find_openssl()
        self.sbsign_path = self.find_sbsign()
        
        logger.info(f"Secure Boot Manager initialized for platform: {self.platform}")
        if self.openssl_path:
            logger.info(f"OpenSSL found at: {self.openssl_path}")
        if self.sbsign_path:
            logger.info(f"sbsign found at: {self.sbsign_path}")
    
    def find_openssl(self) -> Optional[str]:
        """Find OpenSSL executable"""
        try:
            import shutil
            return shutil.which("openssl")
        except:
            return None
    
    def find_sbsign(self) -> Optional[str]:
        """Find sbsign executable (for Linux)"""
        try:
            import shutil
            return shutil.which("sbsign")
        except:
            return None
    
    def is_secure_boot_supported(self) -> bool:
        """
        Check if Secure Boot is supported on current system
        التحقق من دعم Secure Boot على النظام الحالي
        """
        try:
            if self.platform == "windows":
                return self._check_secure_boot_windows()
            elif self.platform == "linux":
                return self._check_secure_boot_linux()
            else:
                return False
                
        except Exception as e:
            logger.error(f"Error checking Secure Boot support: {e}")
            return False
    
    def _check_secure_boot_windows(self) -> bool:
        """Check Secure Boot support on Windows"""
        try:
            # Check if system supports UEFI
            result = subprocess.run(
                ['powershell', '-Command', 'Get-ComputerInfo | Select-Object BiosFirmwareType'],
                capture_output=True, text=True, check=True
            )
            
            if "Uefi" in result.stdout:
                # Check Secure Boot status
                result = subprocess.run(
                    ['powershell', '-Command', 'Confirm-SecureBootUEFI'],
                    capture_output=True, text=True
                )
                
                return result.returncode == 0
            
            return False
            
        except Exception as e:
            logger.warning(f"Could not check Windows Secure Boot status: {e}")
            return False
    
    def _check_secure_boot_linux(self) -> bool:
        """Check Secure Boot support on Linux"""
        try:
            # Check if running on UEFI system
            if not Path("/sys/firmware/efi").exists():
                return False
            
            # Check Secure Boot status
            sb_file = Path("/sys/firmware/efi/efivars/SecureBoot-8be4df61-93ca-11d2-aa0d-00e098032b8c")
            if sb_file.exists():
                with open(sb_file, 'rb') as f:
                    data = f.read()
                    # Last byte indicates Secure Boot status
                    return len(data) > 4 and data[-1] == 1
            
            return False
            
        except Exception as e:
            logger.warning(f"Could not check Linux Secure Boot status: {e}")
            return False
    
    def get_secure_boot_status(self) -> Dict[str, any]:
        """
        Get detailed Secure Boot status
        الحصول على حالة Secure Boot مفصلة
        """
        status = {
            "supported": False,
            "enabled": False,
            "setup_mode": False,
            "platform": self.platform,
            "firmware_type": "unknown"
        }
        
        try:
            if self.platform == "windows":
                status.update(self._get_windows_secure_boot_status())
            elif self.platform == "linux":
                status.update(self._get_linux_secure_boot_status())
            
        except Exception as e:
            logger.error(f"Error getting Secure Boot status: {e}")
        
        return status
    
    def _get_windows_secure_boot_status(self) -> Dict[str, any]:
        """Get Windows Secure Boot status"""
        status = {}
        
        try:
            # Check firmware type
            result = subprocess.run(
                ['powershell', '-Command', 'Get-ComputerInfo | Select-Object BiosFirmwareType'],
                capture_output=True, text=True, check=True
            )
            
            if "Uefi" in result.stdout:
                status["firmware_type"] = "UEFI"
                status["supported"] = True
                
                # Check if Secure Boot is enabled
                result = subprocess.run(
                    ['powershell', '-Command', 'Confirm-SecureBootUEFI'],
                    capture_output=True, text=True
                )
                
                status["enabled"] = result.returncode == 0
                
            else:
                status["firmware_type"] = "BIOS"
                
        except Exception as e:
            logger.warning(f"Error getting Windows Secure Boot status: {e}")
        
        return status
    
    def _get_linux_secure_boot_status(self) -> Dict[str, any]:
        """Get Linux Secure Boot status"""
        status = {}
        
        try:
            # Check if UEFI
            if Path("/sys/firmware/efi").exists():
                status["firmware_type"] = "UEFI"
                status["supported"] = True
                
                # Check Secure Boot enabled
                sb_file = Path("/sys/firmware/efi/efivars/SecureBoot-8be4df61-93ca-11d2-aa0d-00e098032b8c")
                if sb_file.exists():
                    with open(sb_file, 'rb') as f:
                        data = f.read()
                        status["enabled"] = len(data) > 4 and data[-1] == 1
                
                # Check Setup Mode
                sm_file = Path("/sys/firmware/efi/efivars/SetupMode-8be4df61-93ca-11d2-aa0d-00e098032b8c")
                if sm_file.exists():
                    with open(sm_file, 'rb') as f:
                        data = f.read()
                        status["setup_mode"] = len(data) > 4 and data[-1] == 1
                        
            else:
                status["firmware_type"] = "BIOS"
                
        except Exception as e:
            logger.warning(f"Error getting Linux Secure Boot status: {e}")
        
        return status
    
    def generate_signing_keys(self, output_dir: Path, 
                            key_name: str = "oslltn") -> Tuple[bool, str]:
        """
        Generate signing keys for Secure Boot
        إنشاء مفاتيح التوقيع لـ Secure Boot
        
        Args:
            output_dir: Directory to store keys
            key_name: Base name for key files
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if not self.openssl_path:
                return False, "OpenSSL not found. Please install OpenSSL."
            
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate private key
            private_key_file = output_dir / f"{key_name}.key"
            cert_file = output_dir / f"{key_name}.crt"
            
            logger.info("Generating RSA private key...")
            result = subprocess.run([
                self.openssl_path, "genrsa", "-out", str(private_key_file), "2048"
            ], capture_output=True, text=True, check=True)
            
            # Generate self-signed certificate
            logger.info("Generating self-signed certificate...")
            result = subprocess.run([
                self.openssl_path, "req", "-new", "-x509",
                "-key", str(private_key_file),
                "-out", str(cert_file),
                "-days", "365",
                "-subj", "/CN=OSLLTN Secure Boot Key/O=OSLLTN/C=US"
            ], capture_output=True, text=True, check=True)
            
            # Set appropriate permissions
            if self.platform != "windows":
                private_key_file.chmod(0o600)
                cert_file.chmod(0o644)
            
            logger.info(f"Signing keys generated successfully in {output_dir}")
            return True, f"Signing keys generated in {output_dir}"
            
        except subprocess.CalledProcessError as e:
            return False, f"Key generation failed: {e.stderr}"
        except Exception as e:
            return False, f"Error generating keys: {e}"
    
    def sign_efi_binary(self, binary_path: Path, cert_path: Path, 
                       key_path: Path, output_path: Optional[Path] = None) -> Tuple[bool, str]:
        """
        Sign EFI binary for Secure Boot
        توقيع ملف EFI لـ Secure Boot
        
        Args:
            binary_path: Path to EFI binary to sign
            cert_path: Path to certificate file
            key_path: Path to private key file
            output_path: Optional output path (defaults to overwriting input)
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if not binary_path.exists():
                return False, f"Binary file not found: {binary_path}"
            
            if not cert_path.exists():
                return False, f"Certificate file not found: {cert_path}"
            
            if not key_path.exists():
                return False, f"Key file not found: {key_path}"
            
            if output_path is None:
                output_path = binary_path
            
            if self.platform == "linux" and self.sbsign_path:
                return self._sign_with_sbsign(binary_path, cert_path, key_path, output_path)
            elif self.openssl_path:
                return self._sign_with_openssl(binary_path, cert_path, key_path, output_path)
            else:
                return False, "No signing tools available"
                
        except Exception as e:
            return False, f"Error signing EFI binary: {e}"
    
    def _sign_with_sbsign(self, binary_path: Path, cert_path: Path, 
                         key_path: Path, output_path: Path) -> Tuple[bool, str]:
        """Sign EFI binary using sbsign (Linux)"""
        try:
            cmd = [
                self.sbsign_path,
                "--key", str(key_path),
                "--cert", str(cert_path),
                "--output", str(output_path),
                str(binary_path)
            ]
            
            result = subprocess.run(
                cmd, capture_output=True, text=True, check=True
            )
            
            logger.info(f"EFI binary signed successfully: {output_path}")
            return True, f"EFI binary signed: {output_path}"
            
        except subprocess.CalledProcessError as e:
            return False, f"sbsign failed: {e.stderr}"
    
    def _sign_with_openssl(self, binary_path: Path, cert_path: Path, 
                          key_path: Path, output_path: Path) -> Tuple[bool, str]:
        """Sign EFI binary using OpenSSL (basic implementation)"""
        try:
            # This is a simplified implementation
            # For production use, consider using proper EFI signing tools
            
            # Create a detached signature
            with tempfile.NamedTemporaryFile(suffix=".sig", delete=False) as sig_file:
                sig_path = Path(sig_file.name)
            
            try:
                # Create signature
                cmd = [
                    self.openssl_path, "dgst", "-sha256", "-sign", str(key_path),
                    "-out", str(sig_path), str(binary_path)
                ]
                
                result = subprocess.run(
                    cmd, capture_output=True, text=True, check=True
                )
                
                # For now, just copy the original file
                # In a real implementation, you would embed the signature
                import shutil
                shutil.copy2(binary_path, output_path)
                
                logger.info(f"EFI binary processed: {output_path}")
                return True, f"EFI binary processed: {output_path}"
                
            finally:
                sig_path.unlink(missing_ok=True)
                
        except subprocess.CalledProcessError as e:
            return False, f"OpenSSL signing failed: {e.stderr}"
        except Exception as e:
            return False, f"Error in OpenSSL signing: {e}"
    
    def verify_signature(self, binary_path: Path, cert_path: Path) -> Tuple[bool, str]:
        """
        Verify EFI binary signature
        التحقق من توقيع ملف EFI
        
        Args:
            binary_path: Path to signed EFI binary
            cert_path: Path to certificate file
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if not binary_path.exists():
                return False, f"Binary file not found: {binary_path}"
            
            if not cert_path.exists():
                return False, f"Certificate file not found: {cert_path}"
            
            if self.platform == "linux":
                # Use sbverify if available
                sbverify_path = self.find_sbverify()
                if sbverify_path:
                    return self._verify_with_sbverify(binary_path, cert_path)
            
            # Fallback verification (basic)
            return self._verify_basic(binary_path, cert_path)
            
        except Exception as e:
            return False, f"Error verifying signature: {e}"
    
    def find_sbverify(self) -> Optional[str]:
        """Find sbverify executable"""
        try:
            import shutil
            return shutil.which("sbverify")
        except:
            return None
    
    def _verify_with_sbverify(self, binary_path: Path, cert_path: Path) -> Tuple[bool, str]:
        """Verify signature using sbverify"""
        try:
            sbverify_path = self.find_sbverify()
            if not sbverify_path:
                return False, "sbverify not found"
            
            cmd = [
                sbverify_path,
                "--cert", str(cert_path),
                str(binary_path)
            ]
            
            result = subprocess.run(
                cmd, capture_output=True, text=True, check=True
            )
            
            return True, "Signature verification passed"
            
        except subprocess.CalledProcessError as e:
            return False, f"Signature verification failed: {e.stderr}"
    
    def _verify_basic(self, binary_path: Path, cert_path: Path) -> Tuple[bool, str]:
        """Basic signature verification"""
        try:
            # This is a placeholder for basic verification
            # In a real implementation, you would check the embedded signature
            
            logger.info(f"Basic verification for: {binary_path}")
            return True, "Basic verification completed (placeholder)"
            
        except Exception as e:
            return False, f"Basic verification failed: {e}"
    
    def get_secure_boot_keys_info(self) -> Dict[str, List[str]]:
        """
        Get information about Secure Boot keys in the system
        الحصول على معلومات مفاتيح Secure Boot في النظام
        """
        keys_info = {
            "platform_keys": [],
            "key_exchange_keys": [],
            "signature_database": [],
            "forbidden_database": []
        }
        
        try:
            if self.platform == "linux" and Path("/sys/firmware/efi").exists():
                keys_info.update(self._get_linux_sb_keys())
            elif self.platform == "windows":
                keys_info.update(self._get_windows_sb_keys())
                
        except Exception as e:
            logger.error(f"Error getting Secure Boot keys info: {e}")
        
        return keys_info
    
    def _get_linux_sb_keys(self) -> Dict[str, List[str]]:
        """Get Secure Boot keys information on Linux"""
        keys_info = {}
        
        try:
            efi_vars_path = Path("/sys/firmware/efi/efivars")
            
            # Platform Key (PK)
            pk_files = list(efi_vars_path.glob("PK-*"))
            keys_info["platform_keys"] = [f.name for f in pk_files]
            
            # Key Exchange Key (KEK)
            kek_files = list(efi_vars_path.glob("KEK-*"))
            keys_info["key_exchange_keys"] = [f.name for f in kek_files]
            
            # Signature Database (db)
            db_files = list(efi_vars_path.glob("db-*"))
            keys_info["signature_database"] = [f.name for f in db_files]
            
            # Forbidden Database (dbx)
            dbx_files = list(efi_vars_path.glob("dbx-*"))
            keys_info["forbidden_database"] = [f.name for f in dbx_files]
            
        except Exception as e:
            logger.warning(f"Error getting Linux Secure Boot keys: {e}")
        
        return keys_info
    
    def _get_windows_sb_keys(self) -> Dict[str, List[str]]:
        """Get Secure Boot keys information on Windows"""
        keys_info = {}
        
        try:
            # This would require more complex Windows API calls
            # For now, return placeholder
            keys_info = {
                "platform_keys": ["Windows Platform Key"],
                "key_exchange_keys": ["Microsoft KEK"],
                "signature_database": ["Microsoft Windows Production"],
                "forbidden_database": ["Various revoked certificates"]
            }
            
        except Exception as e:
            logger.warning(f"Error getting Windows Secure Boot keys: {e}")
        
        return keys_info
    
    def get_installation_requirements(self) -> Dict[str, any]:
        """
        Get requirements for Secure Boot functionality
        الحصول على متطلبات وظائف Secure Boot
        """
        requirements = {
            "openssl": {
                "required": True,
                "available": self.openssl_path is not None,
                "path": self.openssl_path,
                "install_command": self._get_openssl_install_command()
            }
        }
        
        if self.platform == "linux":
            requirements["sbsign"] = {
                "required": True,
                "available": self.sbsign_path is not None,
                "path": self.sbsign_path,
                "install_command": self._get_sbsign_install_command()
            }
        
        return requirements
    
    def _get_openssl_install_command(self) -> str:
        """Get OpenSSL installation command for current platform"""
        if self.platform == "windows":
            return "Download from: https://slproweb.com/products/Win32OpenSSL.html"
        elif self.platform == "linux":
            return "sudo apt-get install openssl (Ubuntu/Debian) or sudo dnf install openssl (Fedora)"
        elif self.platform == "darwin":
            return "brew install openssl"
        else:
            return "Please install OpenSSL for your platform"
    
    def _get_sbsign_install_command(self) -> str:
        """Get sbsign installation command for Linux"""
        return "sudo apt-get install sbsigntool (Ubuntu/Debian) or sudo dnf install sbsigntools (Fedora)"
