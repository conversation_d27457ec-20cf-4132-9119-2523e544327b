# 🚀 OSLLTN Setup Guide - دليل إعداد OSLLTN

## ✅ تم إنشاء المشروع بنجاح! / Project Created Successfully!

لقد تم إنشاء مشروع **OSLLTN Multi-Boot USB Creator** بنجاح مع جميع الملفات والميزات المطلوبة.

The **OSLLTN Multi-Boot USB Creator** project has been successfully created with all required files and features.

## 📋 ما تم إنجازه / What Has Been Completed

### ✅ الملفات الأساسية / Core Files
- `main.py` - نقطة الدخول الرئيسية
- `run.py` - سكريبت التشغيل مع فحص المتطلبات
- `requirements.txt` - جميع التبعيات المطلوبة
- `setup.py` & `pyproject.toml` - إعداد التثبيت

### ✅ الوحدات الأساسية / Core Modules
- `core/disk_manager.py` - إدارة أجهزة USB والتقسيم
- `core/bootloader.py` - تثبيت GRUB2 و wimboot
- `core/iso_manager.py` - إدارة ملفات ISO/WIM/IMG
- `core/grub_config.py` - إنشاء تكوين GRUB
- `core/checksum.py` - حساب والتحقق من checksums

### ✅ الواجهة الرسومية / GUI
- `gui/main_window.py` - واجهة PyQt6 كاملة مع الوضع الليلي

### ✅ الأدوات المساعدة / Utilities
- `utils/platform_utils.py` - أدوات خاصة بكل منصة
- `utils/qemu_test.py` - اختبار QEMU
- `utils/secure_boot.py` - دعم Secure Boot

### ✅ التوثيق / Documentation
- `README.md` - توثيق شامل
- `docs/user-guide.md` - دليل المستخدم
- `QUICK_START.md` - دليل البداية السريعة

### ✅ سكريبتات التثبيت / Installation Scripts
- `install.bat` / `install.sh` - تثبيت تلقائي
- `run.bat` / `run.sh` - تشغيل مع فحص المتطلبات
- `install_python.bat` - تثبيت Python تلقائي

## 🐍 الخطوة التالية: تثبيت Python / Next Step: Install Python

**المشكلة الحالية**: Python غير مثبت على النظام
**Current Issue**: Python is not installed on the system

### 📥 تثبيت Python على Windows / Installing Python on Windows

#### الطريقة 1: التحميل المباشر / Direct Download
1. **اذهب إلى**: https://www.python.org/downloads/
2. **انقر على**: "Download Python 3.11.x" (أحدث إصدار)
3. **شغل المثبت** وتأكد من:
   - ✅ وضع علامة على **"Add Python to PATH"**
   - ✅ اختيار **"Install for all users"** (اختياري)
4. **انقر**: "Install Now"
5. **انتظر** حتى يكتمل التثبيت
6. **أعد تشغيل** Command Prompt

#### الطريقة 2: Microsoft Store
1. افتح **Microsoft Store**
2. ابحث عن **"Python 3.11"**
3. انقر **"Get"** أو **"Install"**

#### الطريقة 3: winget (Windows 10/11)
```cmd
winget install Python.Python.3.11
```

### 🔧 التحقق من التثبيت / Verify Installation

بعد تثبيت Python، افتح **Command Prompt جديد** وشغل:
After installing Python, open a **new Command Prompt** and run:

```cmd
python --version
pip --version
```

يجب أن ترى شيئاً مثل:
You should see something like:
```
Python 3.11.x
pip 23.x.x
```

## 🚀 تشغيل OSLLTN / Running OSLLTN

بعد تثبيت Python بنجاح:
After successfully installing Python:

### الطريقة 1: السكريبت التلقائي / Automatic Script
```cmd
# انقر نقراً مزدوجاً أو شغل / Double-click or run:
run_simple.bat
```

### الطريقة 2: التثبيت اليدوي / Manual Installation
```cmd
# تثبيت المتطلبات / Install requirements
pip install -r requirements.txt

# تشغيل البرنامج / Run the program
python run.py
```

### الطريقة 3: كمدير / As Administrator
```cmd
# انقر بالزر الأيمن على Command Prompt واختر "Run as administrator"
# Right-click Command Prompt and select "Run as administrator"

python run.py
```

## 📋 متطلبات النظام / System Requirements

### الحد الأدنى / Minimum
- **OS**: Windows 10 أو أحدث
- **RAM**: 2 GB
- **Storage**: 500 MB مساحة فارغة
- **Python**: 3.8 أو أحدث

### الموصى به / Recommended
- **OS**: Windows 11
- **RAM**: 4 GB أو أكثر
- **Storage**: 1 GB مساحة فارغة
- **USB**: فلاش USB 3.0 بحجم 16 GB أو أكبر

## 🔧 استكشاف الأخطاء / Troubleshooting

### المشكلة: "Python was not found"
**الحل**:
1. تأكد من تثبيت Python
2. تأكد من إضافة Python إلى PATH
3. أعد تشغيل Command Prompt
4. جرب `py` بدلاً من `python`

### المشكلة: "pip is not recognized"
**الحل**:
```cmd
python -m pip --version
```

### المشكلة: مشاكل في الصلاحيات
**الحل**:
- شغل Command Prompt كمدير
- انقر بالزر الأيمن واختر "Run as administrator"

## 📁 بنية المشروع / Project Structure

```
oslltn/
├── 📄 main.py                    # نقطة الدخول
├── 📄 run.py                     # سكريبت التشغيل
├── 📄 requirements.txt           # التبعيات
├── 📄 run_simple.bat            # تشغيل مبسط
├── 📄 install_python.bat        # تثبيت Python
├── 📂 core/                      # الوحدات الأساسية
├── 📂 gui/                       # الواجهة الرسومية
├── 📂 utils/                     # الأدوات المساعدة
├── 📂 boot/                      # ملفات الإقلاع
└── 📂 docs/                      # التوثيق
```

## 🎯 الميزات المتوفرة / Available Features

### ✨ الميزات الأساسية / Core Features
- ✅ دعم متعدد المنصات (Windows/Linux/macOS)
- ✅ واجهة رسومية حديثة مع الوضع الليلي
- ✅ دعم ملفات ISO/WIM/IMG/ESD
- ✅ إقلاع UEFI و Legacy BIOS
- ✅ كشف تلقائي لأنظمة التشغيل

### 🔧 الميزات المتقدمة / Advanced Features
- ✅ حساب والتحقق من Checksums
- ✅ اختبار QEMU
- ✅ دعم Secure Boot
- ✅ قائمة إقلاع ديناميكية
- ✅ دعم متعدد اللغات

## 📞 الدعم / Support

إذا واجهت أي مشاكل:
If you encounter any issues:

1. **راجع التوثيق** / **Check Documentation**:
   - `README.md`
   - `docs/user-guide.md`
   - `QUICK_START.md`

2. **تحقق من المتطلبات** / **Verify Requirements**:
   - Python 3.8+
   - صلاحيات المدير
   - اتصال إنترنت للتحميل

3. **شغل الاختبارات** / **Run Tests**:
   ```cmd
   python test_basic.py
   ```

4. **اطلب المساعدة** / **Get Help**:
   - GitHub Issues
   - Email: <EMAIL>

## 🎉 مبروك! / Congratulations!

لقد تم إنشاء مشروع OSLLTN بنجاح! الآن كل ما تحتاجه هو تثبيت Python وستكون جاهزاً لإنشاء فلاش USB متعدد الإقلاع.

The OSLLTN project has been successfully created! Now all you need is to install Python and you'll be ready to create multi-boot USB drives.

---

**الخطوة التالية**: تثبيت Python من https://www.python.org/downloads/
**Next Step**: Install Python from https://www.python.org/downloads/
