[build-system]
requires = ["setuptools>=45", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[project]
name = "oslltn"
version = "1.0.0"
description = "Multi-Boot USB Creator - نظام إنشاء فلاش USB متعدد الإقلاع"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "OSLLTN Project", email = "<EMAIL>"}
]
maintainers = [
    {name = "OSLLTN Project", email = "<EMAIL>"}
]
keywords = [
    "usb", "boot", "multiboot", "grub", "uefi", "bios",
    "iso", "wim", "windows", "linux", "ventoy"
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "Intended Audience :: System Administrators",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: System :: Boot",
    "Topic :: System :: Systems Administration",
    "Topic :: Utilities",
]
requires-python = ">=3.8"
dependencies = [
    "PyQt6>=6.5.0",
    "psutil>=5.9.0",
    "pycdlib>=1.13.0",
    "pycryptodome>=3.18.0",
    "requests>=2.31.0",
    "python-magic>=0.4.27",
    "pywin32>=306; sys_platform == 'win32'",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "black>=22.0.0",
    "flake8>=4.0.0",
    "mypy>=0.950",
    "isort>=5.10.0",
]
docs = [
    "sphinx>=4.0.0",
    "sphinx-rtd-theme>=1.0.0",
    "myst-parser>=0.18.0",
]
test = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
]

[project.urls]
Homepage = "https://github.com/oslltn/multiboot-usb-creator"
Documentation = "https://oslltn.readthedocs.io/"
Repository = "https://github.com/oslltn/multiboot-usb-creator.git"
"Bug Tracker" = "https://github.com/oslltn/multiboot-usb-creator/issues"
Changelog = "https://github.com/oslltn/multiboot-usb-creator/blob/main/CHANGELOG.md"

[project.scripts]
oslltn = "main:main"
oslltn-gui = "main:main"

[project.gui-scripts]
oslltn-gui = "main:main"

[tool.setuptools]
packages = ["core", "gui", "utils"]
include-package-data = true

[tool.setuptools.package-data]
"*" = ["*.cfg", "*.efi", "*.qm", "*.ts", "*.md", "*.rst"]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["core", "gui", "utils"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "PyQt6.*",
    "pycdlib.*",
    "win32api.*",
    "win32file.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["core", "gui", "utils"]
omit = [
    "*/tests/*",
    "*/test_*",
    "setup.py",
    "run.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
