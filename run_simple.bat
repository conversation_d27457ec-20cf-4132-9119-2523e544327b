@echo off
chcp 65001 >nul
echo ========================================
echo OSLLTN Multi-Boot USB Creator
echo ========================================
echo.

REM Check if Python is installed
echo Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed
    echo.
    echo Please install Python 3.8+ from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo Python found:
python --version
echo.

REM Check if requirements are installed
echo Checking requirements...
python -c "import PyQt6" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing requirements...
    python -m pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install requirements
        pause
        exit /b 1
    )
)

echo Requirements satisfied
echo.

echo Starting OSLLTN...
python run.py

if %errorlevel% neq 0 (
    echo.
    echo Application exited with error
    pause
)

echo.
echo Application closed
pause
