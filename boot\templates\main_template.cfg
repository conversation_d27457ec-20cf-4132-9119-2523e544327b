# OSLLTN Multi-Boot USB Configuration
# تكوين فلاش USB متعدد الإقلاع
# Generated automatically - Do not edit manually
# تم إنشاؤه تلقائياً - لا تقم بالتعديل يدوياً

set timeout=10
set default=0

# Load video modules
insmod all_video
insmod gfxterm
insmod png
insmod jpeg

# Set graphics mode
set gfxmode=auto
terminal_output gfxterm

# Set theme colors
set color_normal=white/black
set color_highlight=black/light-cyan
set menu_color_normal=white/black
set menu_color_highlight=black/light-cyan

# Load additional modules
insmod part_gpt
insmod part_msdos
insmod fat
insmod exfat
insmod ntfs
insmod ext2
insmod iso9660
insmod loopback
insmod chain

# Set root device (will be set dynamically)
# تعيين الجهاز الجذر (سيتم تعيينه ديناميكياً)
search --no-floppy --fs-uuid --set=root {USB_UUID}

# Main menu header
menuentry "OSLLTN Multi-Boot USB Creator" --class oslltn {
    echo "OSLLTN Multi-Boot USB Creator"
    echo "نظام إنشاء فلاش USB متعدد الإقلاع"
    echo ""
    echo "Select an operating system to boot:"
    echo "اختر نظام التشغيل للإقلاع:"
    echo ""
    sleep 3
}

menuentry "--- Operating Systems ---" --class separator {
    true
}

{BOOT_ENTRIES}

menuentry "--- Advanced Options ---" --class separator {
    true
}

submenu "System Tools" --class tools {
    menuentry "Memory Test (memtest86+)" --class memtest {
        if [ -f /boot/memtest86+.bin ]; then
            linux16 /boot/memtest86+.bin
        else
            echo "Memory test not available"
            echo "اختبار الذاكرة غير متوفر"
            sleep 2
        fi
    }

    menuentry "Hardware Detection Tool" --class hardware {
        if [ -f /boot/hdt.c32 ]; then
            linux /boot/hdt.c32
        else
            echo "Hardware detection tool not available"
            echo "أداة اكتشاف الأجهزة غير متوفرة"
            sleep 2
        fi
    }

    menuentry "System Information" --class info {
        echo "Platform: $grub_platform"
        echo "CPU: $grub_cpu"
        echo "Memory: Available"
        echo ""
        echo "المنصة: $grub_platform"
        echo "المعالج: $grub_cpu"
        echo "الذاكرة: متوفرة"
        sleep 5
    }
}

submenu "Boot Options" --class options {
    menuentry "Boot from Hard Disk" --class harddisk {
        echo "Booting from first hard disk..."
        echo "الإقلاع من القرص الصلب الأول..."
        set root=(hd1)
        chainloader +1
        boot
    }

    menuentry "Boot from Network (PXE)" --class network {
        echo "Attempting network boot..."
        echo "محاولة الإقلاع من الشبكة..."
        pxe_boot
    }
}

submenu "System Control" --class control {
    menuentry "Reboot" --class reboot {
        echo "Rebooting system..."
        echo "إعادة تشغيل النظام..."
        reboot
    }

    menuentry "Shutdown" --class shutdown {
        echo "Shutting down system..."
        echo "إيقاف تشغيل النظام..."
        halt
    }

    menuentry "UEFI Firmware Settings" --class firmware {
        echo "Entering UEFI firmware settings..."
        echo "الدخول إلى إعدادات UEFI..."
        fwsetup
    }
}
