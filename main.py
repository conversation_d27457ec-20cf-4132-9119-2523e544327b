#!/usr/bin/env python3
"""
OSLLTN - Multi-Boot USB Creator
نظام إنشاء فلاش USB متعدد الإقلاع

Main entry point for the application.
نقطة البداية الرئيسية للتطبيق.
"""

import sys
import os
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from gui.main_window import MainWindow

def setup_logging():
    """Setup logging configuration"""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "oslltn.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )

def load_translations(app):
    """Load application translations"""
    translator = QTranslator()
    locale = QLocale.system().name()
    
    # Try to load translation file
    translation_file = project_root / "locales" / f"{locale}.qm"
    if translation_file.exists():
        translator.load(str(translation_file))
        app.installTranslator(translator)
    
    return translator

def main():
    """Main application entry point"""
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("Starting OSLLTN Multi-Boot USB Creator")
    
    # Create QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("OSLLTN")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("OSLLTN Project")
    
    # Load translations
    translator = load_translations(app)
    
    # Create and show main window
    try:
        main_window = MainWindow()
        main_window.show()
        
        logger.info("Application started successfully")
        return app.exec()
        
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
