"""
Main Window for OSLLTN Multi-Boot USB Creator
النافذة الرئيسية لنظام إنشاء فلاش USB متعدد الإقلاع
"""

import sys
import os
import logging
from typing import List, Optional
from pathlib import Path

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QComboBox, QProgressBar, QTextEdit,
    QGroupBox, QListWidget, QListWidgetItem, QFileDialog,
    QMessageBox, QSplitter, QTabWidget, QCheckBox, QSpinBox,
    QStatusBar, QMenuBar, QMenu, QToolBar, QFrame
)
from PyQt6.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QSettings, QSize
)
from PyQt6.QtGui import (
    QIcon, QPixmap, QFont, QAction, QPalette, QColor
)

from core.disk_manager import DiskManager, USBDevice
from core.bootloader import BootloaderManager
from core.iso_manager import ISOManager, ISOMetadata
from core.grub_config import GRUBConfigGenerator

logger = logging.getLogger(__name__)

class WorkerThread(QThread):
    """Worker thread for long-running operations"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)
    
    def __init__(self, operation, *args, **kwargs):
        super().__init__()
        self.operation = operation
        self.args = args
        self.kwargs = kwargs
    
    def run(self):
        try:
            result = self.operation(*self.args, **self.kwargs)
            if isinstance(result, tuple):
                success, message = result
                self.finished.emit(success, message)
            else:
                self.finished.emit(True, "Operation completed successfully")
        except Exception as e:
            logger.error(f"Worker thread error: {e}")
            self.finished.emit(False, str(e))

class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize managers
        self.disk_manager = DiskManager()
        self.bootloader_manager = BootloaderManager()
        self.iso_manager = ISOManager()
        self.grub_generator = GRUBConfigGenerator()
        
        # Initialize UI
        self.init_ui()
        self.setup_connections()
        self.load_settings()
        
        # Initialize data
        self.usb_devices = []
        self.iso_files = []
        self.selected_device = None
        
        # Start periodic refresh
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_usb_devices)
        self.refresh_timer.start(5000)  # Refresh every 5 seconds
        
        # Initial refresh
        self.refresh_usb_devices()
        
        logger.info("Main window initialized")
    
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("OSLLTN Multi-Boot USB Creator - نظام إنشاء فلاش USB متعدد الإقلاع")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # Set application icon
        self.setWindowIcon(self.create_app_icon())
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create menu bar
        self.create_menu_bar()
        
        # Create toolbar
        self.create_toolbar()
        
        # Create main content
        self.create_main_content(main_layout)
        
        # Create status bar
        self.create_status_bar()
        
        # Apply dark theme
        self.apply_dark_theme()
    
    def create_app_icon(self) -> QIcon:
        """Create application icon"""
        # Create a simple icon using text
        pixmap = QPixmap(64, 64)
        pixmap.fill(QColor(0, 120, 215))  # Blue background
        
        return QIcon(pixmap)
    
    def create_menu_bar(self):
        """Create menu bar"""
        menubar = self.menuBar()
        
        # File menu
        file_menu = menubar.addMenu("File / ملف")
        
        refresh_action = QAction("Refresh Devices / تحديث الأجهزة", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_usb_devices)
        file_menu.addAction(refresh_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("Exit / خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # Tools menu
        tools_menu = menubar.addMenu("Tools / أدوات")
        
        clear_cache_action = QAction("Clear Cache / مسح التخزين المؤقت", self)
        clear_cache_action.triggered.connect(self.clear_cache)
        tools_menu.addAction(clear_cache_action)
        
        download_boot_files_action = QAction("Download Boot Files / تحميل ملفات الإقلاع", self)
        download_boot_files_action.triggered.connect(self.download_boot_files)
        tools_menu.addAction(download_boot_files_action)
        
        # Help menu
        help_menu = menubar.addMenu("Help / مساعدة")
        
        about_action = QAction("About / حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """Create toolbar"""
        toolbar = self.addToolBar("Main Toolbar")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        
        # Refresh action
        refresh_action = QAction("Refresh / تحديث", self)
        refresh_action.triggered.connect(self.refresh_usb_devices)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        # Prepare USB action
        self.prepare_action = QAction("Prepare USB / تحضير USB", self)
        self.prepare_action.triggered.connect(self.prepare_usb)
        self.prepare_action.setEnabled(False)
        toolbar.addAction(self.prepare_action)
        
        # Add ISOs action
        add_isos_action = QAction("Add ISOs / إضافة ISOs", self)
        add_isos_action.triggered.connect(self.add_iso_files)
        toolbar.addAction(add_isos_action)
        
        toolbar.addSeparator()
        
        # Test boot action
        self.test_boot_action = QAction("Test Boot / اختبار الإقلاع", self)
        self.test_boot_action.triggered.connect(self.test_boot)
        self.test_boot_action.setEnabled(False)
        toolbar.addAction(self.test_boot_action)
    
    def create_main_content(self, main_layout):
        """Create main content area"""
        # Create splitter for main content
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # Left panel - USB devices and settings
        left_panel = self.create_left_panel()
        splitter.addWidget(left_panel)
        
        # Right panel - ISO files and operations
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
    
    def create_left_panel(self) -> QWidget:
        """Create left panel with USB devices and settings"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # USB Devices group
        usb_group = QGroupBox("USB Devices / أجهزة USB")
        usb_layout = QVBoxLayout(usb_group)
        
        # USB device selection
        self.usb_combo = QComboBox()
        self.usb_combo.currentTextChanged.connect(self.on_usb_device_changed)
        usb_layout.addWidget(QLabel("Select USB Device / اختر جهاز USB:"))
        usb_layout.addWidget(self.usb_combo)
        
        # Device info
        self.device_info_label = QLabel("No device selected / لم يتم اختيار جهاز")
        self.device_info_label.setWordWrap(True)
        usb_layout.addWidget(self.device_info_label)
        
        layout.addWidget(usb_group)
        
        # Settings group
        settings_group = QGroupBox("Settings / الإعدادات")
        settings_layout = QGridLayout(settings_group)
        
        # ESP partition size
        settings_layout.addWidget(QLabel("ESP Size (MB) / حجم ESP:"), 0, 0)
        self.esp_size_spin = QSpinBox()
        self.esp_size_spin.setRange(256, 2048)
        self.esp_size_spin.setValue(512)
        self.esp_size_spin.setSuffix(" MB")
        settings_layout.addWidget(self.esp_size_spin, 0, 1)
        
        # Secure Boot support
        self.secure_boot_check = QCheckBox("Enable Secure Boot / تفعيل Secure Boot")
        settings_layout.addWidget(self.secure_boot_check, 1, 0, 1, 2)
        
        # Auto-refresh
        self.auto_refresh_check = QCheckBox("Auto-refresh devices / تحديث تلقائي للأجهزة")
        self.auto_refresh_check.setChecked(True)
        self.auto_refresh_check.toggled.connect(self.toggle_auto_refresh)
        settings_layout.addWidget(self.auto_refresh_check, 2, 0, 1, 2)
        
        layout.addWidget(settings_group)
        
        # Progress group
        progress_group = QGroupBox("Progress / التقدم")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("Ready / جاهز")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # Stretch
        layout.addStretch()
        
        return panel
    
    def create_right_panel(self) -> QWidget:
        """Create right panel with ISO files and operations"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Create tab widget
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # ISO Files tab
        iso_tab = self.create_iso_tab()
        tab_widget.addTab(iso_tab, "ISO Files / ملفات ISO")
        
        # Operations tab
        operations_tab = self.create_operations_tab()
        tab_widget.addTab(operations_tab, "Operations / العمليات")
        
        # Log tab
        log_tab = self.create_log_tab()
        tab_widget.addTab(log_tab, "Log / السجل")
        
        return panel
    
    def create_iso_tab(self) -> QWidget:
        """Create ISO files tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # ISO files list
        iso_group = QGroupBox("ISO Files / ملفات ISO")
        iso_layout = QVBoxLayout(iso_group)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("Add Files / إضافة ملفات")
        add_button.clicked.connect(self.add_iso_files)
        buttons_layout.addWidget(add_button)
        
        scan_button = QPushButton("Scan Directory / فحص مجلد")
        scan_button.clicked.connect(self.scan_directory)
        buttons_layout.addWidget(scan_button)
        
        remove_button = QPushButton("Remove Selected / حذف المحدد")
        remove_button.clicked.connect(self.remove_selected_iso)
        buttons_layout.addWidget(remove_button)
        
        buttons_layout.addStretch()
        
        checksum_button = QPushButton("Calculate Checksums / حساب Checksums")
        checksum_button.clicked.connect(self.calculate_checksums)
        buttons_layout.addWidget(checksum_button)
        
        iso_layout.addLayout(buttons_layout)
        
        # ISO list widget
        self.iso_list = QListWidget()
        self.iso_list.itemSelectionChanged.connect(self.on_iso_selection_changed)
        iso_layout.addWidget(self.iso_list)
        
        # ISO details
        self.iso_details = QTextEdit()
        self.iso_details.setMaximumHeight(150)
        self.iso_details.setReadOnly(True)
        iso_layout.addWidget(QLabel("File Details / تفاصيل الملف:"))
        iso_layout.addWidget(self.iso_details)
        
        layout.addWidget(iso_group)
        
        return tab
    
    def create_operations_tab(self) -> QWidget:
        """Create operations tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Main operations
        main_ops_group = QGroupBox("Main Operations / العمليات الرئيسية")
        main_ops_layout = QVBoxLayout(main_ops_group)
        
        self.prepare_button = QPushButton("Prepare USB Device / تحضير جهاز USB")
        self.prepare_button.clicked.connect(self.prepare_usb)
        self.prepare_button.setEnabled(False)
        main_ops_layout.addWidget(self.prepare_button)
        
        self.install_bootloader_button = QPushButton("Install Bootloader / تثبيت محمل الإقلاع")
        self.install_bootloader_button.clicked.connect(self.install_bootloader)
        self.install_bootloader_button.setEnabled(False)
        main_ops_layout.addWidget(self.install_bootloader_button)
        
        self.generate_config_button = QPushButton("Generate Boot Menu / إنشاء قائمة الإقلاع")
        self.generate_config_button.clicked.connect(self.generate_boot_config)
        self.generate_config_button.setEnabled(False)
        main_ops_layout.addWidget(self.generate_config_button)
        
        layout.addWidget(main_ops_group)
        
        # Advanced operations
        advanced_ops_group = QGroupBox("Advanced Operations / العمليات المتقدمة")
        advanced_ops_layout = QVBoxLayout(advanced_ops_group)
        
        self.update_bootloader_button = QPushButton("Update Bootloader / تحديث محمل الإقلاع")
        self.update_bootloader_button.clicked.connect(self.update_bootloader)
        self.update_bootloader_button.setEnabled(False)
        advanced_ops_layout.addWidget(self.update_bootloader_button)
        
        self.verify_button = QPushButton("Verify Installation / التحقق من التثبيت")
        self.verify_button.clicked.connect(self.verify_installation)
        self.verify_button.setEnabled(False)
        advanced_ops_layout.addWidget(self.verify_button)
        
        self.test_qemu_button = QPushButton("Test in QEMU / اختبار في QEMU")
        self.test_qemu_button.clicked.connect(self.test_boot)
        self.test_qemu_button.setEnabled(False)
        advanced_ops_layout.addWidget(self.test_qemu_button)
        
        layout.addWidget(advanced_ops_group)
        
        # Stretch
        layout.addStretch()
        
        return tab
    
    def create_log_tab(self) -> QWidget:
        """Create log tab"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Log controls
        controls_layout = QHBoxLayout()
        
        clear_log_button = QPushButton("Clear Log / مسح السجل")
        clear_log_button.clicked.connect(self.clear_log)
        controls_layout.addWidget(clear_log_button)
        
        save_log_button = QPushButton("Save Log / حفظ السجل")
        save_log_button.clicked.connect(self.save_log)
        controls_layout.addWidget(save_log_button)
        
        controls_layout.addStretch()
        
        layout.addLayout(controls_layout)
        
        # Log text area
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        return tab
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # Add permanent widgets
        self.device_count_label = QLabel("Devices: 0")
        self.status_bar.addPermanentWidget(self.device_count_label)
        
        self.iso_count_label = QLabel("ISOs: 0")
        self.status_bar.addPermanentWidget(self.iso_count_label)
        
        self.status_bar.showMessage("Ready / جاهز")
    
    def apply_dark_theme(self):
        """Apply dark theme to the application"""
        dark_palette = QPalette()
        
        # Window colors
        dark_palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ColorRole.WindowText, QColor(255, 255, 255))
        
        # Base colors
        dark_palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
        dark_palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
        
        # Text colors
        dark_palette.setColor(QPalette.ColorRole.Text, QColor(255, 255, 255))
        dark_palette.setColor(QPalette.ColorRole.BrightText, QColor(255, 0, 0))
        
        # Button colors
        dark_palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
        dark_palette.setColor(QPalette.ColorRole.ButtonText, QColor(255, 255, 255))
        
        # Highlight colors
        dark_palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
        dark_palette.setColor(QPalette.ColorRole.HighlightedText, QColor(0, 0, 0))
        
        self.setPalette(dark_palette)
    
    def setup_connections(self):
        """Setup signal connections"""
        pass
    
    def load_settings(self):
        """Load application settings"""
        settings = QSettings("OSLLTN", "MultiBootUSB")
        
        # Window geometry
        geometry = settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        
        # ESP size
        esp_size = settings.value("esp_size", 512, type=int)
        self.esp_size_spin.setValue(esp_size)
        
        # Secure Boot
        secure_boot = settings.value("secure_boot", False, type=bool)
        self.secure_boot_check.setChecked(secure_boot)
        
        # Auto refresh
        auto_refresh = settings.value("auto_refresh", True, type=bool)
        self.auto_refresh_check.setChecked(auto_refresh)
    
    def save_settings(self):
        """Save application settings"""
        settings = QSettings("OSLLTN", "MultiBootUSB")
        
        # Window geometry
        settings.setValue("geometry", self.saveGeometry())
        
        # ESP size
        settings.setValue("esp_size", self.esp_size_spin.value())
        
        # Secure Boot
        settings.setValue("secure_boot", self.secure_boot_check.isChecked())
        
        # Auto refresh
        settings.setValue("auto_refresh", self.auto_refresh_check.isChecked())
    
    def closeEvent(self, event):
        """Handle window close event"""
        self.save_settings()
        event.accept()
    
    # Slot methods
    def refresh_usb_devices(self):
        """Refresh USB devices list"""
        try:
            self.usb_devices = self.disk_manager.get_usb_devices()
            
            # Update combo box
            current_text = self.usb_combo.currentText()
            self.usb_combo.clear()
            
            if not self.usb_devices:
                self.usb_combo.addItem("No USB devices found / لم يتم العثور على أجهزة USB")
                self.device_info_label.setText("No USB devices detected / لم يتم اكتشاف أجهزة USB")
                self.selected_device = None
            else:
                for device in self.usb_devices:
                    self.usb_combo.addItem(str(device))
                
                # Try to restore previous selection
                index = self.usb_combo.findText(current_text)
                if index >= 0:
                    self.usb_combo.setCurrentIndex(index)
                else:
                    self.usb_combo.setCurrentIndex(0)
            
            # Update device count
            self.device_count_label.setText(f"Devices: {len(self.usb_devices)}")
            
            # Update button states
            self.update_button_states()
            
        except Exception as e:
            logger.error(f"Error refreshing USB devices: {e}")
            self.show_error("Error refreshing USB devices", str(e))
    
    def on_usb_device_changed(self, text):
        """Handle USB device selection change"""
        try:
            if not self.usb_devices or text.startswith("No USB"):
                self.selected_device = None
                self.device_info_label.setText("No device selected / لم يتم اختيار جهاز")
            else:
                # Find selected device
                for device in self.usb_devices:
                    if str(device) == text:
                        self.selected_device = device
                        break
                
                if self.selected_device:
                    info = f"""Device: {self.selected_device.label}
Path: {self.selected_device.device_path}
Size: {self.selected_device.size_human_readable()}
Filesystem: {self.selected_device.filesystem}

الجهاز: {self.selected_device.label}
المسار: {self.selected_device.device_path}
الحجم: {self.selected_device.size_human_readable()}
نظام الملفات: {self.selected_device.filesystem}"""
                    self.device_info_label.setText(info)
            
            # Update button states
            self.update_button_states()
            
        except Exception as e:
            logger.error(f"Error handling device selection: {e}")
    
    def update_button_states(self):
        """Update button enabled states"""
        has_device = self.selected_device is not None
        has_isos = len(self.iso_files) > 0
        
        # Enable/disable buttons based on state
        self.prepare_action.setEnabled(has_device)
        self.prepare_button.setEnabled(has_device)
        self.install_bootloader_button.setEnabled(has_device)
        self.generate_config_button.setEnabled(has_device and has_isos)
        self.update_bootloader_button.setEnabled(has_device)
        self.verify_button.setEnabled(has_device)
        self.test_boot_action.setEnabled(has_device)
        self.test_qemu_button.setEnabled(has_device)
    
    def toggle_auto_refresh(self, enabled):
        """Toggle auto-refresh timer"""
        if enabled:
            self.refresh_timer.start(5000)
        else:
            self.refresh_timer.stop()
    
    def add_iso_files(self):
        """Add ISO files dialog"""
        try:
            file_dialog = QFileDialog()
            files, _ = file_dialog.getOpenFileNames(
                self,
                "Select ISO/WIM/IMG files / اختر ملفات ISO/WIM/IMG",
                "",
                "Boot Images (*.iso *.wim *.img *.esd);;All Files (*.*)"
            )
            
            if files:
                self.add_files_to_list(files)
                
        except Exception as e:
            logger.error(f"Error adding ISO files: {e}")
            self.show_error("Error adding files", str(e))
    
    def scan_directory(self):
        """Scan directory for ISO files"""
        try:
            directory = QFileDialog.getExistingDirectory(
                self,
                "Select directory to scan / اختر مجلد للفحص"
            )
            
            if directory:
                self.status_label.setText("Scanning directory... / فحص المجلد...")
                self.progress_bar.setVisible(True)
                self.progress_bar.setRange(0, 0)  # Indeterminate progress
                
                # Scan in worker thread
                self.worker = WorkerThread(self.iso_manager.scan_directory, Path(directory))
                self.worker.finished.connect(self.on_scan_finished)
                self.worker.start()
                
        except Exception as e:
            logger.error(f"Error scanning directory: {e}")
            self.show_error("Error scanning directory", str(e))
    
    def on_scan_finished(self, success, result):
        """Handle scan completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            if isinstance(result, list):
                self.iso_files.extend(result)
                self.update_iso_list()
                self.log_message(f"Found {len(result)} bootable files")
            else:
                self.log_message("Scan completed")
        else:
            self.show_error("Scan failed", str(result))
    
    def add_files_to_list(self, file_paths):
        """Add files to ISO list"""
        try:
            for file_path in file_paths:
                path = Path(file_path)
                if path.exists():
                    # Extract metadata
                    metadata = self.iso_manager._extract_metadata(path)
                    if metadata:
                        self.iso_files.append(metadata)
            
            self.update_iso_list()
            
        except Exception as e:
            logger.error(f"Error adding files to list: {e}")
            self.show_error("Error adding files", str(e))
    
    def update_iso_list(self):
        """Update ISO list widget"""
        self.iso_list.clear()
        
        for iso_file in self.iso_files:
            item = QListWidgetItem()
            item.setText(f"{iso_file.os_name} ({iso_file.filename})")
            item.setData(Qt.ItemDataRole.UserRole, iso_file)
            
            # Set icon based on OS type
            if iso_file.os_type == 'windows':
                item.setToolTip("Windows ISO")
            elif iso_file.os_type == 'linux':
                item.setToolTip("Linux ISO")
            else:
                item.setToolTip("Generic bootable image")
            
            self.iso_list.addItem(item)
        
        # Update count
        self.iso_count_label.setText(f"ISOs: {len(self.iso_files)}")
        
        # Update button states
        self.update_button_states()
    
    def on_iso_selection_changed(self):
        """Handle ISO selection change"""
        current_item = self.iso_list.currentItem()
        if current_item:
            iso_file = current_item.data(Qt.ItemDataRole.UserRole)
            if iso_file:
                details = f"""Filename: {iso_file.filename}
OS Type: {iso_file.os_type}
OS Name: {iso_file.os_name}
OS Version: {iso_file.os_version}
Architecture: {iso_file.architecture}
Size: {iso_file.size / (1024*1024*1024):.2f} GB
Bootable: {'Yes' if iso_file.bootable else 'No'}
File Type: {iso_file.file_type.upper()}

اسم الملف: {iso_file.filename}
نوع النظام: {iso_file.os_type}
اسم النظام: {iso_file.os_name}
إصدار النظام: {iso_file.os_version}
المعمارية: {iso_file.architecture}
الحجم: {iso_file.size / (1024*1024*1024):.2f} جيجابايت
قابل للإقلاع: {'نعم' if iso_file.bootable else 'لا'}
نوع الملف: {iso_file.file_type.upper()}"""

                if iso_file.checksum_md5:
                    details += f"\nMD5: {iso_file.checksum_md5}"
                if iso_file.checksum_sha256:
                    details += f"\nSHA256: {iso_file.checksum_sha256}"
                
                self.iso_details.setText(details)
        else:
            self.iso_details.clear()
    
    def remove_selected_iso(self):
        """Remove selected ISO from list"""
        current_item = self.iso_list.currentItem()
        if current_item:
            iso_file = current_item.data(Qt.ItemDataRole.UserRole)
            if iso_file in self.iso_files:
                self.iso_files.remove(iso_file)
            
            self.update_iso_list()
    
    def calculate_checksums(self):
        """Calculate checksums for all ISO files"""
        if not self.iso_files:
            self.show_info("No files", "No ISO files to process")
            return
        
        reply = QMessageBox.question(
            self,
            "Calculate Checksums",
            "This operation may take a long time for large files. Continue?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.status_label.setText("Calculating checksums... / حساب checksums...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, len(self.iso_files))
            
            # Calculate checksums in worker thread
            self.worker = WorkerThread(self.calculate_all_checksums)
            self.worker.progress_updated.connect(self.progress_bar.setValue)
            self.worker.finished.connect(self.on_checksums_finished)
            self.worker.start()
    
    def calculate_all_checksums(self):
        """Calculate checksums for all files"""
        for i, iso_file in enumerate(self.iso_files):
            self.worker.progress_updated.emit(i)
            self.iso_manager.update_metadata_checksums(iso_file)
        
        return True, "Checksums calculated successfully"
    
    def on_checksums_finished(self, success, message):
        """Handle checksum calculation completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            self.update_iso_list()
            self.log_message("Checksums calculated successfully")
        else:
            self.show_error("Checksum calculation failed", message)
    
    def prepare_usb(self):
        """Prepare USB device"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        reply = QMessageBox.warning(
            self,
            "Warning / تحذير",
            f"This will erase all data on {self.selected_device.label}!\n"
            f"هذا سيمحو جميع البيانات على {self.selected_device.label}!\n\n"
            "Are you sure you want to continue?\n"
            "هل أنت متأكد من أنك تريد المتابعة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.status_label.setText("Preparing USB device... / تحضير جهاز USB...")
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)
            
            # Prepare in worker thread
            esp_size = self.esp_size_spin.value()
            self.worker = WorkerThread(
                self.disk_manager.prepare_usb_device,
                self.selected_device,
                esp_size
            )
            self.worker.finished.connect(self.on_prepare_finished)
            self.worker.start()
    
    def on_prepare_finished(self, success, message):
        """Handle USB preparation completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            self.log_message(f"USB device prepared successfully: {message}")
            self.show_info("Success", "USB device prepared successfully!")
        else:
            self.log_message(f"USB preparation failed: {message}")
            self.show_error("Preparation failed", message)
    
    def install_bootloader(self):
        """Install bootloader"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        self.status_label.setText("Installing bootloader... / تثبيت محمل الإقلاع...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        # Get ESP partition path
        partitions = self.disk_manager.get_device_partitions(self.selected_device)
        esp_partition = partitions.get('esp')
        
        if not esp_partition:
            self.show_error("Error", "Could not find ESP partition")
            return
        
        # Install in worker thread
        self.worker = WorkerThread(
            self.bootloader_manager.install_bootloader,
            esp_partition
        )
        self.worker.finished.connect(self.on_bootloader_finished)
        self.worker.start()
    
    def on_bootloader_finished(self, success, message):
        """Handle bootloader installation completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            self.log_message(f"Bootloader installed successfully: {message}")
            self.show_info("Success", "Bootloader installed successfully!")
        else:
            self.log_message(f"Bootloader installation failed: {message}")
            self.show_error("Installation failed", message)
    
    def generate_boot_config(self):
        """Generate boot configuration"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        if not self.iso_files:
            self.show_warning("No ISO files", "Please add some ISO files first")
            return
        
        self.status_label.setText("Generating boot configuration... / إنشاء تكوين الإقلاع...")
        
        try:
            # Generate GRUB configuration
            config = self.grub_generator.generate_config(self.iso_files)
            
            # Get ESP partition path
            partitions = self.disk_manager.get_device_partitions(self.selected_device)
            esp_partition = partitions.get('esp')
            
            if esp_partition:
                success = self.grub_generator.save_config(config, esp_partition)
                if success:
                    self.log_message("Boot configuration generated successfully")
                    self.show_info("Success", "Boot configuration generated successfully!")
                else:
                    self.show_error("Error", "Failed to save boot configuration")
            else:
                self.show_error("Error", "Could not find ESP partition")
        
        except Exception as e:
            logger.error(f"Error generating boot config: {e}")
            self.show_error("Error generating configuration", str(e))
        
        self.status_label.setText("Ready / جاهز")
    
    def update_bootloader(self):
        """Update bootloader"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        self.status_label.setText("Updating bootloader... / تحديث محمل الإقلاع...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        # Get ESP partition path
        partitions = self.disk_manager.get_device_partitions(self.selected_device)
        esp_partition = partitions.get('esp')
        
        if not esp_partition:
            self.show_error("Error", "Could not find ESP partition")
            return
        
        # Update in worker thread
        self.worker = WorkerThread(
            self.bootloader_manager.update_bootloader,
            esp_partition
        )
        self.worker.finished.connect(self.on_update_finished)
        self.worker.start()
    
    def on_update_finished(self, success, message):
        """Handle bootloader update completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            self.log_message(f"Bootloader updated successfully: {message}")
            self.show_info("Success", "Bootloader updated successfully!")
        else:
            self.log_message(f"Bootloader update failed: {message}")
            self.show_error("Update failed", message)
    
    def verify_installation(self):
        """Verify bootloader installation"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        try:
            # Get ESP partition path
            partitions = self.disk_manager.get_device_partitions(self.selected_device)
            esp_partition = partitions.get('esp')
            
            if esp_partition:
                success, message = self.bootloader_manager.verify_bootloader_installation(esp_partition)
                if success:
                    self.show_info("Verification successful", message)
                else:
                    self.show_warning("Verification failed", message)
            else:
                self.show_error("Error", "Could not find ESP partition")
        
        except Exception as e:
            logger.error(f"Error verifying installation: {e}")
            self.show_error("Verification error", str(e))
    
    def test_boot(self):
        """Test boot in QEMU"""
        if not self.selected_device:
            self.show_warning("No device selected", "Please select a USB device first")
            return
        
        self.show_info("Test Boot", "QEMU testing not implemented yet")
    
    def download_boot_files(self):
        """Download boot files"""
        self.status_label.setText("Downloading boot files... / تحميل ملفات الإقلاع...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)
        
        # Download in worker thread
        self.worker = WorkerThread(self.bootloader_manager.download_boot_files)
        self.worker.finished.connect(self.on_download_finished)
        self.worker.start()
    
    def on_download_finished(self, success, message):
        """Handle boot files download completion"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("Ready / جاهز")
        
        if success:
            self.log_message(f"Boot files downloaded successfully: {message}")
            self.show_info("Success", "Boot files downloaded successfully!")
        else:
            self.log_message(f"Boot files download failed: {message}")
            self.show_error("Download failed", message)
    
    def clear_cache(self):
        """Clear metadata cache"""
        self.iso_manager.clear_cache()
        self.log_message("Metadata cache cleared")
        self.show_info("Cache cleared", "Metadata cache has been cleared")
    
    def clear_log(self):
        """Clear log text"""
        self.log_text.clear()
    
    def save_log(self):
        """Save log to file"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "Save Log File",
                "oslltn_log.txt",
                "Text Files (*.txt);;All Files (*.*)"
            )
            
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                
                self.show_info("Log saved", f"Log saved to: {file_path}")
        
        except Exception as e:
            self.show_error("Error saving log", str(e))
    
    def log_message(self, message):
        """Add message to log"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        logger.info(message)
    
    def show_info(self, title, message):
        """Show information dialog"""
        QMessageBox.information(self, title, message)
    
    def show_warning(self, title, message):
        """Show warning dialog"""
        QMessageBox.warning(self, title, message)
    
    def show_error(self, title, message):
        """Show error dialog"""
        QMessageBox.critical(self, title, message)
    
    def show_about(self):
        """Show about dialog"""
        about_text = """
<h2>OSLLTN Multi-Boot USB Creator</h2>
<h3>نظام إنشاء فلاش USB متعدد الإقلاع</h3>

<p><b>Version:</b> 1.0.0</p>
<p><b>الإصدار:</b> 1.0.0</p>

<p>A powerful tool for creating multi-boot USB drives with support for:</p>
<p>أداة قوية لإنشاء فلاش USB متعدد الإقلاع مع دعم:</p>

<ul>
<li>Windows ISO/WIM files</li>
<li>Linux distributions</li>
<li>UEFI and Legacy BIOS boot</li>
<li>Secure Boot support</li>
<li>Multiple platforms (Windows, Linux, macOS)</li>
</ul>

<p><b>Technologies used:</b></p>
<p><b>التقنيات المستخدمة:</b></p>
<ul>
<li>Python 3.8+</li>
<li>PyQt6</li>
<li>GRUB2</li>
<li>wimboot</li>
</ul>

<p><b>License:</b> MIT</p>
<p><b>الترخيص:</b> MIT</p>
"""
        
        QMessageBox.about(self, "About OSLLTN", about_text)
