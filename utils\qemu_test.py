"""
QEMU Testing Utilities
أدوات اختبار QEMU

Provides functionality to test USB boot in QEMU virtual machine
توفر وظائف لاختبار إقلاع USB في آلة QEMU الافتراضية
"""

import os
import sys
import logging
import subprocess
import platform
import shutil
from typing import Optional, Tuple, Dict, List
from pathlib import Path
import threading
import time

logger = logging.getLogger(__name__)

class QEMUTester:
    """QEMU virtual machine tester for USB boot"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        self.qemu_path = self.find_qemu_executable()
        self.process = None
        self.is_running = False
        
        logger.info(f"QEMU Tester initialized for platform: {self.platform}")
        if self.qemu_path:
            logger.info(f"QEMU found at: {self.qemu_path}")
        else:
            logger.warning("QEMU not found in system PATH")
    
    def find_qemu_executable(self) -> Optional[str]:
        """
        Find QEMU executable in system PATH
        البحث عن ملف QEMU التنفيذي في مسار النظام
        """
        try:
            # Common QEMU executable names
            qemu_names = [
                "qemu-system-x86_64",
                "qemu-system-i386",
                "qemu",
                "qemu.exe"
            ]
            
            for qemu_name in qemu_names:
                qemu_path = shutil.which(qemu_name)
                if qemu_path:
                    return qemu_path
            
            # Check common installation paths
            common_paths = []
            
            if self.platform == "windows":
                common_paths = [
                    "C:\\Program Files\\qemu\\qemu-system-x86_64.exe",
                    "C:\\Program Files (x86)\\qemu\\qemu-system-x86_64.exe",
                    "C:\\qemu\\qemu-system-x86_64.exe"
                ]
            elif self.platform == "linux":
                common_paths = [
                    "/usr/bin/qemu-system-x86_64",
                    "/usr/local/bin/qemu-system-x86_64",
                    "/opt/qemu/bin/qemu-system-x86_64"
                ]
            elif self.platform == "darwin":
                common_paths = [
                    "/usr/local/bin/qemu-system-x86_64",
                    "/opt/homebrew/bin/qemu-system-x86_64",
                    "/Applications/QEMU.app/Contents/MacOS/qemu-system-x86_64"
                ]
            
            for path in common_paths:
                if Path(path).exists():
                    return path
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding QEMU executable: {e}")
            return None
    
    def is_qemu_available(self) -> bool:
        """
        Check if QEMU is available
        التحقق من توفر QEMU
        """
        return self.qemu_path is not None
    
    def get_qemu_version(self) -> Optional[str]:
        """
        Get QEMU version
        الحصول على إصدار QEMU
        """
        try:
            if not self.qemu_path:
                return None
            
            result = subprocess.run(
                [self.qemu_path, "--version"],
                capture_output=True, text=True, check=True
            )
            
            # Parse version from output
            lines = result.stdout.strip().split('\n')
            if lines:
                version_line = lines[0]
                # Extract version number
                parts = version_line.split()
                for part in parts:
                    if part.replace('.', '').replace('-', '').isdigit():
                        return part
            
            return "Unknown"
            
        except Exception as e:
            logger.error(f"Error getting QEMU version: {e}")
            return None
    
    def create_test_configuration(self, usb_device_path: str, 
                                memory_mb: int = 2048,
                                enable_kvm: bool = True,
                                boot_mode: str = "uefi") -> Dict[str, any]:
        """
        Create QEMU test configuration
        إنشاء تكوين اختبار QEMU
        
        Args:
            usb_device_path: Path to USB device or image file
            memory_mb: Amount of memory in MB
            enable_kvm: Enable KVM acceleration (Linux only)
            boot_mode: Boot mode ('uefi' or 'bios')
            
        Returns:
            Configuration dictionary
        """
        config = {
            "memory": memory_mb,
            "usb_device": usb_device_path,
            "boot_mode": boot_mode,
            "enable_kvm": enable_kvm and self.platform == "linux",
            "enable_graphics": True,
            "enable_audio": False,
            "cpu_cores": 2,
            "network": False
        }
        
        return config
    
    def build_qemu_command(self, config: Dict[str, any]) -> List[str]:
        """
        Build QEMU command line arguments
        بناء معاملات سطر أوامر QEMU
        """
        try:
            cmd = [self.qemu_path]
            
            # Memory
            cmd.extend(["-m", str(config["memory"])])
            
            # CPU
            cmd.extend(["-smp", str(config["cpu_cores"])])
            
            # Enable KVM if available and requested
            if config.get("enable_kvm", False) and self.platform == "linux":
                if self._is_kvm_available():
                    cmd.extend(["-enable-kvm"])
                    logger.info("KVM acceleration enabled")
                else:
                    logger.warning("KVM not available, using software emulation")
            
            # Boot mode
            if config["boot_mode"] == "uefi":
                # Use OVMF UEFI firmware
                ovmf_path = self._find_ovmf_firmware()
                if ovmf_path:
                    cmd.extend(["-bios", ovmf_path])
                    logger.info("UEFI boot mode enabled")
                else:
                    logger.warning("OVMF firmware not found, falling back to BIOS")
            
            # USB device
            usb_device = config["usb_device"]
            if Path(usb_device).exists():
                if self.platform == "windows":
                    # On Windows, use the device directly
                    cmd.extend(["-drive", f"file={usb_device},format=raw,if=none,id=usb"])
                    cmd.extend(["-device", "usb-storage,drive=usb,bootindex=1"])
                else:
                    # On Linux/macOS, use the device file
                    cmd.extend(["-drive", f"file={usb_device},format=raw,if=none,id=usb"])
                    cmd.extend(["-device", "usb-storage,drive=usb,bootindex=1"])
            
            # Graphics
            if config.get("enable_graphics", True):
                cmd.extend(["-vga", "std"])
            else:
                cmd.extend(["-nographic"])
            
            # Network
            if not config.get("network", False):
                cmd.extend(["-netdev", "none"])
            
            # Audio
            if not config.get("enable_audio", False):
                cmd.extend(["-audiodev", "none,id=none"])
            
            # Boot order
            cmd.extend(["-boot", "order=c"])
            
            # Monitor
            cmd.extend(["-monitor", "stdio"])
            
            logger.info(f"QEMU command: {' '.join(cmd)}")
            return cmd
            
        except Exception as e:
            logger.error(f"Error building QEMU command: {e}")
            return []
    
    def _is_kvm_available(self) -> bool:
        """Check if KVM is available on Linux"""
        try:
            return Path("/dev/kvm").exists()
        except:
            return False
    
    def _find_ovmf_firmware(self) -> Optional[str]:
        """Find OVMF UEFI firmware"""
        try:
            ovmf_paths = []
            
            if self.platform == "linux":
                ovmf_paths = [
                    "/usr/share/ovmf/OVMF.fd",
                    "/usr/share/edk2-ovmf/OVMF_CODE.fd",
                    "/usr/share/qemu/OVMF.fd"
                ]
            elif self.platform == "windows":
                # QEMU for Windows usually includes OVMF
                qemu_dir = Path(self.qemu_path).parent
                ovmf_paths = [
                    str(qemu_dir / "share" / "edk2-x86_64-code.fd"),
                    str(qemu_dir / "OVMF.fd")
                ]
            elif self.platform == "darwin":
                ovmf_paths = [
                    "/usr/local/share/qemu/edk2-x86_64-code.fd",
                    "/opt/homebrew/share/qemu/edk2-x86_64-code.fd"
                ]
            
            for path in ovmf_paths:
                if Path(path).exists():
                    return path
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding OVMF firmware: {e}")
            return None
    
    def start_test(self, config: Dict[str, any], 
                  output_callback=None) -> Tuple[bool, str]:
        """
        Start QEMU test
        بدء اختبار QEMU
        
        Args:
            config: Test configuration
            output_callback: Optional callback for output
            
        Returns:
            Tuple of (success, message)
        """
        try:
            if not self.qemu_path:
                return False, "QEMU not found. Please install QEMU first."
            
            if self.is_running:
                return False, "QEMU test is already running"
            
            # Build command
            cmd = self.build_qemu_command(config)
            if not cmd:
                return False, "Failed to build QEMU command"
            
            # Start QEMU process
            logger.info("Starting QEMU test...")
            
            self.process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            self.is_running = True
            
            # Start output monitoring thread
            if output_callback:
                self.output_thread = threading.Thread(
                    target=self._monitor_output,
                    args=(output_callback,)
                )
                self.output_thread.daemon = True
                self.output_thread.start()
            
            logger.info("QEMU test started successfully")
            return True, "QEMU test started successfully"
            
        except Exception as e:
            error_msg = f"Error starting QEMU test: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _monitor_output(self, callback):
        """Monitor QEMU output in separate thread"""
        try:
            while self.is_running and self.process:
                output = self.process.stdout.readline()
                if output:
                    callback(output.strip())
                elif self.process.poll() is not None:
                    break
                    
        except Exception as e:
            logger.error(f"Error monitoring QEMU output: {e}")
    
    def send_command(self, command: str) -> bool:
        """
        Send command to QEMU monitor
        إرسال أمر إلى مراقب QEMU
        """
        try:
            if not self.is_running or not self.process:
                return False
            
            self.process.stdin.write(command + '\n')
            self.process.stdin.flush()
            
            logger.info(f"Sent command to QEMU: {command}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending command to QEMU: {e}")
            return False
    
    def stop_test(self) -> Tuple[bool, str]:
        """
        Stop QEMU test
        إيقاف اختبار QEMU
        """
        try:
            if not self.is_running:
                return True, "QEMU test is not running"
            
            logger.info("Stopping QEMU test...")
            
            # Try graceful shutdown first
            if self.process:
                self.send_command("quit")
                
                # Wait for process to terminate
                try:
                    self.process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    # Force kill if graceful shutdown fails
                    logger.warning("QEMU did not respond to quit command, forcing termination")
                    self.process.kill()
                    self.process.wait()
            
            self.is_running = False
            self.process = None
            
            logger.info("QEMU test stopped")
            return True, "QEMU test stopped successfully"
            
        except Exception as e:
            error_msg = f"Error stopping QEMU test: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_status(self) -> Dict[str, any]:
        """
        Get QEMU test status
        الحصول على حالة اختبار QEMU
        """
        status = {
            "is_running": self.is_running,
            "qemu_available": self.is_qemu_available(),
            "qemu_path": self.qemu_path,
            "qemu_version": self.get_qemu_version(),
            "platform": self.platform
        }
        
        if self.process:
            status["process_id"] = self.process.pid
            status["return_code"] = self.process.poll()
        
        return status
    
    def create_disk_image(self, image_path: str, size_mb: int) -> Tuple[bool, str]:
        """
        Create a disk image for testing
        إنشاء صورة قرص للاختبار
        """
        try:
            if not self.qemu_path:
                return False, "QEMU not found"
            
            # Use qemu-img to create disk image
            qemu_img_path = self.qemu_path.replace("qemu-system-x86_64", "qemu-img")
            if self.platform == "windows":
                qemu_img_path = qemu_img_path.replace("qemu.exe", "qemu-img.exe")
            
            if not Path(qemu_img_path).exists():
                return False, "qemu-img not found"
            
            cmd = [
                qemu_img_path, "create", "-f", "raw",
                image_path, f"{size_mb}M"
            ]
            
            result = subprocess.run(
                cmd, capture_output=True, text=True, check=True
            )
            
            logger.info(f"Created disk image: {image_path} ({size_mb}MB)")
            return True, f"Disk image created successfully: {image_path}"
            
        except subprocess.CalledProcessError as e:
            return False, f"Failed to create disk image: {e.stderr}"
        except Exception as e:
            return False, f"Error creating disk image: {e}"
    
    def get_installation_instructions(self) -> Dict[str, str]:
        """
        Get QEMU installation instructions for current platform
        الحصول على تعليمات تثبيت QEMU للمنصة الحالية
        """
        instructions = {}
        
        if self.platform == "windows":
            instructions = {
                "title": "Install QEMU on Windows",
                "steps": [
                    "1. Download QEMU for Windows from: https://www.qemu.org/download/#windows",
                    "2. Run the installer and follow the installation wizard",
                    "3. Add QEMU installation directory to your PATH environment variable",
                    "4. Restart the application"
                ],
                "download_url": "https://www.qemu.org/download/#windows"
            }
        elif self.platform == "linux":
            instructions = {
                "title": "Install QEMU on Linux",
                "steps": [
                    "Ubuntu/Debian: sudo apt-get install qemu-system-x86",
                    "Fedora/CentOS: sudo dnf install qemu-system-x86",
                    "Arch Linux: sudo pacman -S qemu",
                    "Or download from: https://www.qemu.org/download/#linux"
                ],
                "download_url": "https://www.qemu.org/download/#linux"
            }
        elif self.platform == "darwin":
            instructions = {
                "title": "Install QEMU on macOS",
                "steps": [
                    "Using Homebrew: brew install qemu",
                    "Using MacPorts: sudo port install qemu",
                    "Or download from: https://www.qemu.org/download/#macos"
                ],
                "download_url": "https://www.qemu.org/download/#macos"
            }
        
        return instructions
