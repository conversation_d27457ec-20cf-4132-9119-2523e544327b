# تثبيت Python - Python Installation Guide

## 🐍 Python غير مثبت على النظام / Python is not installed on the system

لتشغيل OSLLTN، تحتاج إلى تثبيت Python أولاً.
To run OSLLTN, you need to install Python first.

## 📥 تثبيت Python على Windows / Installing Python on Windows

### الطريقة 1: من الموقع الرسمي / Method 1: From Official Website

1. **تحميل Python / Download Python**:
   - اذه<PERSON> إلى: https://www.python.org/downloads/
   - انقر على "Download Python 3.11.x" (أحدث إصدار)
   - Go to: https://www.python.org/downloads/
   - Click "Download Python 3.11.x" (latest version)

2. **تشغيل المثبت / Run the Installer**:
   - شغل الملف المحمل (.exe)
   - ✅ **مهم جداً**: ضع علامة على "Add Python to PATH"
   - ✅ **Very Important**: Check "Add Python to PATH"
   - انقر "Install Now"
   - Click "Install Now"

3. **التحقق من التثبيت / Verify Installation**:
   ```cmd
   python --version
   pip --version
   ```

### الطريقة 2: من Microsoft Store / Method 2: From Microsoft Store

1. افتح Microsoft Store
2. ابحث عن "Python 3.11"
3. انقر "Get" أو "Install"
4. انتظر حتى يكتمل التثبيت

### الطريقة 3: باستخدام Chocolatey / Method 3: Using Chocolatey

```cmd
# إذا كان Chocolatey مثبت / If Chocolatey is installed
choco install python
```

### الطريقة 4: باستخدام winget / Method 4: Using winget

```cmd
# Windows 10/11 مع winget / Windows 10/11 with winget
winget install Python.Python.3.11
```

## 🔧 بعد تثبيت Python / After Installing Python

### 1. إعادة تشغيل Command Prompt / Restart Command Prompt
أغلق وأعد فتح Command Prompt أو PowerShell
Close and reopen Command Prompt or PowerShell

### 2. التحقق من التثبيت / Verify Installation
```cmd
python --version
pip --version
```

يجب أن ترى شيئاً مثل:
You should see something like:
```
Python 3.11.x
pip 23.x.x
```

### 3. تثبيت OSLLTN / Install OSLLTN
```cmd
# الانتقال إلى مجلد المشروع / Navigate to project folder
cd path\to\oslltn

# تثبيت المتطلبات / Install requirements
pip install -r requirements.txt

# تشغيل البرنامج / Run the program
python run.py
```

## 🚨 مشاكل شائعة / Common Issues

### المشكلة: "Python was not found" / Issue: "Python was not found"
**الحل / Solution**:
1. تأكد من تثبيت Python بشكل صحيح
2. تأكد من إضافة Python إلى PATH
3. أعد تشغيل Command Prompt
4. جرب `py` بدلاً من `python`

### المشكلة: "pip is not recognized" / Issue: "pip is not recognized"
**الحل / Solution**:
```cmd
# جرب هذا / Try this:
python -m pip --version

# أو / Or:
py -m pip --version
```

### المشكلة: مشاكل في الصلاحيات / Issue: Permission problems
**الحل / Solution**:
```cmd
# شغل Command Prompt كمدير / Run Command Prompt as Administrator
# انقر بالزر الأيمن على Command Prompt واختر "Run as administrator"
# Right-click Command Prompt and select "Run as administrator"
```

## 📋 متطلبات النظام / System Requirements

- **Windows**: Windows 10 أو أحدث / Windows 10 or later
- **RAM**: 4 GB أو أكثر / 4 GB or more
- **Storage**: 500 MB مساحة فارغة / 500 MB free space
- **Python**: 3.8 أو أحدث / 3.8 or later

## 🔄 الخطوات التالية / Next Steps

بعد تثبيت Python بنجاح:
After successfully installing Python:

1. **أعد تشغيل الكمبيوتر** (موصى به)
   **Restart your computer** (recommended)

2. **افتح Command Prompt جديد**
   **Open a new Command Prompt**

3. **انتقل إلى مجلد OSLLTN**
   **Navigate to OSLLTN folder**

4. **شغل سكريبت التثبيت**
   **Run the installation script**:
   ```cmd
   install.bat
   ```

5. **شغل البرنامج**
   **Run the program**:
   ```cmd
   run.bat
   ```

## 📞 تحتاج مساعدة؟ / Need Help?

إذا واجهت مشاكل في التثبيت:
If you encounter installation problems:

- تحقق من [دليل استكشاف الأخطاء](docs/troubleshooting.md)
- Check the [Troubleshooting Guide](docs/troubleshooting.md)
- أنشئ issue على GitHub
- Create an issue on GitHub
- راسلنا على: <EMAIL>
- Email us at: <EMAIL>

---

**ملاحظة**: تثبيت Python مطلوب مرة واحدة فقط. بعد ذلك يمكنك تشغيل OSLLTN في أي وقت.

**Note**: Python installation is required only once. After that, you can run OSLLTN anytime.
