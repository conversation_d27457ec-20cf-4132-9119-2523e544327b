# OSLLTN Quick Start Guide
## دليل البداية السريعة لـ OSLLTN

## Windows

### 1. Install Python
1. Download Python 3.8+ from https://www.python.org/downloads/
2. Run installer and check "Add Python to PATH"
3. Restart your computer

### 2. Install OSLLTN
1. Double-click `install.bat`
2. Wait for installation to complete

### 3. Run OSLLTN
1. Right-click `run.bat` and select "Run as administrator"
2. Or open Command Prompt as administrator and run: `python run.py`

---

## Linux

### 1. Install Dependencies
```bash
# Ubuntu/Debian
sudo apt-get install python3 python3-pip python3-pyqt6 parted

# Fedora
sudo dnf install python3 python3-pip python3-PyQt6 parted

# Arch Linux
sudo pacman -S python python-pip python-pyqt6 parted
```

### 2. Install OSLLTN
```bash
chmod +x install.sh
./install.sh
```

### 3. Run OSLLTN
```bash
chmod +x run.sh
sudo ./run.sh
```

---

## macOS

### 1. Install Dependencies
```bash
# Install Homebrew if not installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python and dependencies
brew install python-tk
pip3 install PyQt6
```

### 2. Install OSLLTN
```bash
pip3 install -r requirements.txt
```

### 3. Run OSLLTN
```bash
sudo python3 run.py
```

---

## Basic Usage / الاستخدام الأساسي

1. **Select USB Device** / **اختر جهاز USB**
2. **Add ISO Files** / **أضف ملفات ISO**
3. **Prepare USB** / **حضر USB**
4. **Install Bootloader** / **ثبت محمل الإقلاع**
5. **Generate Boot Menu** / **أنشئ قائمة الإقلاع**

## Need Help? / تحتاج مساعدة؟

- Read the full [User Guide](docs/user-guide.md)
- Check [Troubleshooting](docs/troubleshooting.md)
- Visit [GitHub Issues](https://github.com/oslltn/multiboot-usb-creator/issues)
