#!/bin/bash

echo "========================================"
echo "OSLLTN Multi-Boot USB Creator Installer"
echo "نظام تثبيت إنشاء فلاش USB متعدد الإقلاع"
echo "========================================"
echo

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed / Python 3 غير مثبت"
    echo
    echo "Please install Python 3.8+ using your package manager:"
    echo "يرجى تثبيت Python 3.8+ باستخدام مدير الحزم:"
    echo
    echo "Ubuntu/Debian: sudo apt-get install python3 python3-pip"
    echo "Fedora: sudo dnf install python3 python3-pip"
    echo "Arch: sudo pacman -S python python-pip"
    echo
    exit 1
fi

echo "✅ Python found / تم العثور على Python"
python3 --version

# Check if pip is installed
if ! command -v pip3 &> /dev/null; then
    echo "❌ pip3 is not installed / pip3 غير مثبت"
    echo
    echo "Please install pip3 using your package manager"
    echo "يرجى تثبيت pip3 باستخدام مدير الحزم"
    exit 1
fi

echo
echo "📦 Installing dependencies / تثبيت التبعيات..."

# Install system dependencies
echo "Installing system dependencies / تثبيت تبعيات النظام..."

if command -v apt-get &> /dev/null; then
    # Ubuntu/Debian
    sudo apt-get update
    sudo apt-get install -y python3-pyqt6 parted
elif command -v dnf &> /dev/null; then
    # Fedora
    sudo dnf install -y python3-PyQt6 parted
elif command -v pacman &> /dev/null; then
    # Arch Linux
    sudo pacman -S --noconfirm python-pyqt6 parted
else
    echo "⚠️  Unknown package manager. Please install PyQt6 and parted manually."
    echo "⚠️  مدير حزم غير معروف. يرجى تثبيت PyQt6 و parted يدوياً."
fi

# Install Python dependencies
echo "Installing Python dependencies / تثبيت تبعيات Python..."
pip3 install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ Failed to install dependencies / فشل في تثبيت التبعيات"
    exit 1
fi

echo
echo "✅ Installation completed successfully / تم التثبيت بنجاح"
echo
echo "To run the application / لتشغيل التطبيق:"
echo "  sudo python3 run.py"
echo
echo "Or use the run script / أو استخدم سكريبت التشغيل:"
echo "  ./run.sh"
echo

# Make run script executable
chmod +x run.sh

echo "Installation complete! / التثبيت مكتمل!"
