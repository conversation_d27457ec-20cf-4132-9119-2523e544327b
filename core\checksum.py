"""
Checksum Utilities Module
وحدة أدوات Checksum

Provides utilities for calculating and verifying file checksums
توفر أدوات لحساب والتحقق من checksums الملفات
"""

import hashlib
import logging
from typing import Optional, Dict, Tuple
from pathlib import Path
import threading
import time

logger = logging.getLogger(__name__)

class ChecksumCalculator:
    """Utility class for calculating file checksums"""
    
    def __init__(self, chunk_size: int = 8192):
        """
        Initialize checksum calculator
        
        Args:
            chunk_size: Size of chunks to read from file (default 8KB)
        """
        self.chunk_size = chunk_size
        self.cancel_flag = threading.Event()
        
    def calculate_md5(self, file_path: Path, progress_callback=None) -> Optional[str]:
        """
        Calculate MD5 checksum of a file
        حساب MD5 checksum للملف
        
        Args:
            file_path: Path to the file
            progress_callback: Optional callback function for progress updates
            
        Returns:
            MD5 checksum string or None if error
        """
        return self._calculate_hash(file_path, hashlib.md5(), progress_callback)
    
    def calculate_sha256(self, file_path: Path, progress_callback=None) -> Optional[str]:
        """
        Calculate SHA256 checksum of a file
        حساب SHA256 checksum للملف
        
        Args:
            file_path: Path to the file
            progress_callback: Optional callback function for progress updates
            
        Returns:
            SHA256 checksum string or None if error
        """
        return self._calculate_hash(file_path, hashlib.sha256(), progress_callback)
    
    def calculate_sha1(self, file_path: Path, progress_callback=None) -> Optional[str]:
        """
        Calculate SHA1 checksum of a file
        حساب SHA1 checksum للملف
        
        Args:
            file_path: Path to the file
            progress_callback: Optional callback function for progress updates
            
        Returns:
            SHA1 checksum string or None if error
        """
        return self._calculate_hash(file_path, hashlib.sha1(), progress_callback)
    
    def calculate_all(self, file_path: Path, progress_callback=None) -> Dict[str, Optional[str]]:
        """
        Calculate MD5, SHA1, and SHA256 checksums of a file
        حساب MD5 و SHA1 و SHA256 checksums للملف
        
        Args:
            file_path: Path to the file
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dictionary with checksum results
        """
        try:
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return {"md5": None, "sha1": None, "sha256": None}
            
            logger.info(f"Calculating all checksums for: {file_path}")
            
            # Initialize hashers
            md5_hasher = hashlib.md5()
            sha1_hasher = hashlib.sha1()
            sha256_hasher = hashlib.sha256()
            
            file_size = file_path.stat().st_size
            bytes_read = 0
            
            with open(file_path, 'rb') as f:
                while True:
                    if self.cancel_flag.is_set():
                        logger.info("Checksum calculation cancelled")
                        return {"md5": None, "sha1": None, "sha256": None}
                    
                    chunk = f.read(self.chunk_size)
                    if not chunk:
                        break
                    
                    # Update all hashers
                    md5_hasher.update(chunk)
                    sha1_hasher.update(chunk)
                    sha256_hasher.update(chunk)
                    
                    bytes_read += len(chunk)
                    
                    # Report progress
                    if progress_callback and file_size > 0:
                        progress = int((bytes_read / file_size) * 100)
                        progress_callback(progress)
            
            results = {
                "md5": md5_hasher.hexdigest(),
                "sha1": sha1_hasher.hexdigest(),
                "sha256": sha256_hasher.hexdigest()
            }
            
            logger.info(f"Checksums calculated successfully for {file_path}")
            return results
            
        except Exception as e:
            logger.error(f"Error calculating checksums for {file_path}: {e}")
            return {"md5": None, "sha1": None, "sha256": None}
    
    def _calculate_hash(self, file_path: Path, hasher, progress_callback=None) -> Optional[str]:
        """
        Internal method to calculate hash using specified hasher
        طريقة داخلية لحساب hash باستخدام hasher محدد
        """
        try:
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            file_size = file_path.stat().st_size
            bytes_read = 0
            
            with open(file_path, 'rb') as f:
                while True:
                    if self.cancel_flag.is_set():
                        logger.info("Hash calculation cancelled")
                        return None
                    
                    chunk = f.read(self.chunk_size)
                    if not chunk:
                        break
                    
                    hasher.update(chunk)
                    bytes_read += len(chunk)
                    
                    # Report progress
                    if progress_callback and file_size > 0:
                        progress = int((bytes_read / file_size) * 100)
                        progress_callback(progress)
            
            return hasher.hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating hash for {file_path}: {e}")
            return None
    
    def verify_checksum(self, file_path: Path, expected_checksum: str, 
                       algorithm: str = "md5") -> Tuple[bool, str]:
        """
        Verify file checksum against expected value
        التحقق من checksum الملف مقابل القيمة المتوقعة
        
        Args:
            file_path: Path to the file
            expected_checksum: Expected checksum value
            algorithm: Hash algorithm ('md5', 'sha1', 'sha256')
            
        Returns:
            Tuple of (is_valid, message)
        """
        try:
            logger.info(f"Verifying {algorithm} checksum for: {file_path}")
            
            # Calculate current checksum
            if algorithm.lower() == "md5":
                current_checksum = self.calculate_md5(file_path)
            elif algorithm.lower() == "sha1":
                current_checksum = self.calculate_sha1(file_path)
            elif algorithm.lower() == "sha256":
                current_checksum = self.calculate_sha256(file_path)
            else:
                return False, f"Unsupported algorithm: {algorithm}"
            
            if current_checksum is None:
                return False, "Failed to calculate checksum"
            
            # Compare checksums (case-insensitive)
            expected_lower = expected_checksum.lower().strip()
            current_lower = current_checksum.lower().strip()
            
            if expected_lower == current_lower:
                logger.info(f"Checksum verification passed for {file_path}")
                return True, "Checksum verification passed"
            else:
                logger.warning(f"Checksum mismatch for {file_path}")
                logger.warning(f"Expected: {expected_checksum}")
                logger.warning(f"Actual: {current_checksum}")
                return False, f"Checksum mismatch. Expected: {expected_checksum}, Got: {current_checksum}"
                
        except Exception as e:
            error_msg = f"Error verifying checksum: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def cancel_calculation(self):
        """Cancel ongoing checksum calculation"""
        self.cancel_flag.set()
        logger.info("Checksum calculation cancellation requested")
    
    def reset_cancel_flag(self):
        """Reset the cancel flag for new calculations"""
        self.cancel_flag.clear()

class ChecksumDatabase:
    """Database for storing and managing file checksums"""
    
    def __init__(self, db_file: Path):
        """
        Initialize checksum database
        
        Args:
            db_file: Path to database file
        """
        self.db_file = db_file
        self.checksums = {}
        self.load_database()
        
    def load_database(self):
        """Load checksums from database file"""
        try:
            if self.db_file.exists():
                import json
                with open(self.db_file, 'r', encoding='utf-8') as f:
                    self.checksums = json.load(f)
                logger.info(f"Loaded {len(self.checksums)} checksums from database")
            else:
                logger.info("No existing checksum database found, starting fresh")
                
        except Exception as e:
            logger.error(f"Error loading checksum database: {e}")
            self.checksums = {}
    
    def save_database(self):
        """Save checksums to database file"""
        try:
            # Ensure directory exists
            self.db_file.parent.mkdir(parents=True, exist_ok=True)
            
            import json
            with open(self.db_file, 'w', encoding='utf-8') as f:
                json.dump(self.checksums, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(self.checksums)} checksums to database")
            
        except Exception as e:
            logger.error(f"Error saving checksum database: {e}")
    
    def add_checksum(self, file_path: str, checksums: Dict[str, str]):
        """
        Add checksums for a file
        إضافة checksums لملف
        
        Args:
            file_path: Path to the file
            checksums: Dictionary of checksums (algorithm -> checksum)
        """
        try:
            # Get file info
            path = Path(file_path)
            if path.exists():
                stat = path.stat()
                file_info = {
                    "size": stat.st_size,
                    "modified": stat.st_mtime,
                    "checksums": checksums
                }
                
                self.checksums[str(path)] = file_info
                logger.info(f"Added checksums for: {file_path}")
            
        except Exception as e:
            logger.error(f"Error adding checksum for {file_path}: {e}")
    
    def get_checksum(self, file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        Get stored checksum for a file
        الحصول على checksum محفوظ لملف
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm
            
        Returns:
            Checksum string or None if not found
        """
        try:
            file_info = self.checksums.get(str(Path(file_path)))
            if file_info:
                return file_info.get("checksums", {}).get(algorithm)
            return None
            
        except Exception as e:
            logger.error(f"Error getting checksum for {file_path}: {e}")
            return None
    
    def is_checksum_valid(self, file_path: str) -> bool:
        """
        Check if stored checksum is still valid (file not modified)
        التحقق من صحة checksum المحفوظ (الملف لم يتم تعديله)
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if checksum is valid, False otherwise
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return False
            
            file_info = self.checksums.get(str(path))
            if not file_info:
                return False
            
            # Check if file size and modification time match
            stat = path.stat()
            return (file_info.get("size") == stat.st_size and 
                   file_info.get("modified") == stat.st_mtime)
            
        except Exception as e:
            logger.error(f"Error checking checksum validity for {file_path}: {e}")
            return False
    
    def remove_checksum(self, file_path: str):
        """
        Remove checksum entry for a file
        إزالة إدخال checksum لملف
        
        Args:
            file_path: Path to the file
        """
        try:
            key = str(Path(file_path))
            if key in self.checksums:
                del self.checksums[key]
                logger.info(f"Removed checksum for: {file_path}")
            
        except Exception as e:
            logger.error(f"Error removing checksum for {file_path}: {e}")
    
    def cleanup_invalid_entries(self):
        """Remove entries for files that no longer exist"""
        try:
            invalid_keys = []
            
            for file_path in self.checksums.keys():
                if not Path(file_path).exists():
                    invalid_keys.append(file_path)
            
            for key in invalid_keys:
                del self.checksums[key]
            
            if invalid_keys:
                logger.info(f"Cleaned up {len(invalid_keys)} invalid checksum entries")
                self.save_database()
            
        except Exception as e:
            logger.error(f"Error cleaning up checksum database: {e}")
    
    def get_statistics(self) -> Dict[str, int]:
        """Get database statistics"""
        try:
            stats = {
                "total_files": len(self.checksums),
                "valid_files": 0,
                "invalid_files": 0
            }
            
            for file_path in self.checksums.keys():
                if self.is_checksum_valid(file_path):
                    stats["valid_files"] += 1
                else:
                    stats["invalid_files"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting database statistics: {e}")
            return {"total_files": 0, "valid_files": 0, "invalid_files": 0}
