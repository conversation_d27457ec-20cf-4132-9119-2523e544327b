"""
Disk Manager Module
وحدة إدارة الأقراص

Handles USB device detection, partitioning, and formatting
تتعامل مع اكتشاف أجهزة USB والتقسيم والتهيئة
"""

import os
import sys
import logging
import subprocess
import platform
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import psutil

logger = logging.getLogger(__name__)

class USBDevice:
    """Represents a USB storage device"""
    
    def __init__(self, device_path: str, label: str, size: int, filesystem: str = ""):
        self.device_path = device_path
        self.label = label
        self.size = size
        self.filesystem = filesystem
        self.is_removable = True
        self.mount_points = []
    
    def __str__(self):
        return f"{self.label} ({self.size_human_readable()}) - {self.device_path}"
    
    def size_human_readable(self) -> str:
        """Convert size to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if self.size < 1024.0:
                return f"{self.size:.1f} {unit}"
            self.size /= 1024.0
        return f"{self.size:.1f} PB"

class DiskManager:
    """Manages disk operations for USB devices"""
    
    def __init__(self):
        self.platform = platform.system().lower()
        logger.info(f"Initialized DiskManager for platform: {self.platform}")
    
    def get_usb_devices(self) -> List[USBDevice]:
        """
        Get list of all USB storage devices
        الحصول على قائمة بجميع أجهزة التخزين USB
        """
        try:
            if self.platform == "windows":
                return self._get_usb_devices_windows()
            elif self.platform == "linux":
                return self._get_usb_devices_linux()
            elif self.platform == "darwin":  # macOS
                return self._get_usb_devices_macos()
            else:
                logger.error(f"Unsupported platform: {self.platform}")
                return []
        except Exception as e:
            logger.error(f"Error getting USB devices: {e}")
            return []
    
    def _get_usb_devices_windows(self) -> List[USBDevice]:
        """Get USB devices on Windows"""
        devices = []
        
        try:
            # Use WMI to get removable drives
            import win32api
            import win32file
            
            drives = win32api.GetLogicalDriveStrings()
            drives = drives.split('\000')[:-1]
            
            for drive in drives:
                drive_type = win32file.GetDriveType(drive)
                if drive_type == win32file.DRIVE_REMOVABLE:
                    try:
                        # Get drive info
                        free_bytes, total_bytes = win32api.GetDiskFreeSpace(drive)[:2]
                        label = win32api.GetVolumeInformation(drive)[0]
                        
                        device = USBDevice(
                            device_path=drive,
                            label=label or f"USB Drive ({drive})",
                            size=total_bytes,
                            filesystem="FAT32"  # Default assumption
                        )
                        devices.append(device)
                        
                    except Exception as e:
                        logger.warning(f"Could not get info for drive {drive}: {e}")
                        
        except ImportError:
            logger.warning("pywin32 not available, using psutil fallback")
            # Fallback to psutil
            for partition in psutil.disk_partitions():
                if 'removable' in partition.opts:
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        device = USBDevice(
                            device_path=partition.device,
                            label=f"USB Drive ({partition.device})",
                            size=usage.total,
                            filesystem=partition.fstype
                        )
                        devices.append(device)
                    except Exception as e:
                        logger.warning(f"Could not get usage for {partition.device}: {e}")
        
        return devices
    
    def _get_usb_devices_linux(self) -> List[USBDevice]:
        """Get USB devices on Linux"""
        devices = []
        
        try:
            # Use lsblk to get block devices
            result = subprocess.run(
                ['lsblk', '-J', '-o', 'NAME,SIZE,LABEL,FSTYPE,MOUNTPOINT,TRAN'],
                capture_output=True, text=True, check=True
            )
            
            import json
            data = json.loads(result.stdout)
            
            for device in data.get('blockdevices', []):
                # Check if it's a USB device
                if device.get('tran') == 'usb':
                    size_str = device.get('size', '0B')
                    size = self._parse_size_string(size_str)
                    
                    usb_device = USBDevice(
                        device_path=f"/dev/{device['name']}",
                        label=device.get('label') or f"USB Device ({device['name']})",
                        size=size,
                        filesystem=device.get('fstype', '')
                    )
                    
                    if device.get('mountpoint'):
                        usb_device.mount_points.append(device['mountpoint'])
                    
                    devices.append(usb_device)
                    
        except subprocess.CalledProcessError as e:
            logger.error(f"lsblk command failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse lsblk output: {e}")
        except Exception as e:
            logger.error(f"Error getting USB devices on Linux: {e}")
        
        return devices
    
    def _get_usb_devices_macos(self) -> List[USBDevice]:
        """Get USB devices on macOS"""
        devices = []
        
        try:
            # Use diskutil to get disk information
            result = subprocess.run(
                ['diskutil', 'list', '-plist'],
                capture_output=True, text=True, check=True
            )
            
            import plistlib
            data = plistlib.loads(result.stdout.encode())
            
            for disk in data.get('AllDisks', []):
                # Get detailed info for each disk
                info_result = subprocess.run(
                    ['diskutil', 'info', '-plist', disk],
                    capture_output=True, text=True, check=True
                )
                
                disk_info = plistlib.loads(info_result.stdout.encode())
                
                # Check if it's a removable USB device
                if (disk_info.get('Removable', False) and 
                    'USB' in disk_info.get('BusProtocol', '')):
                    
                    device = USBDevice(
                        device_path=f"/dev/{disk}",
                        label=disk_info.get('VolumeName') or f"USB Device ({disk})",
                        size=disk_info.get('TotalSize', 0),
                        filesystem=disk_info.get('FilesystemType', '')
                    )
                    devices.append(device)
                    
        except subprocess.CalledProcessError as e:
            logger.error(f"diskutil command failed: {e}")
        except Exception as e:
            logger.error(f"Error getting USB devices on macOS: {e}")
        
        return devices
    
    def _parse_size_string(self, size_str: str) -> int:
        """Parse size string like '8G', '512M' to bytes"""
        if not size_str:
            return 0
        
        size_str = size_str.upper().strip()
        multipliers = {
            'B': 1,
            'K': 1024,
            'M': 1024**2,
            'G': 1024**3,
            'T': 1024**4
        }
        
        for suffix, multiplier in multipliers.items():
            if size_str.endswith(suffix):
                try:
                    number = float(size_str[:-1])
                    return int(number * multiplier)
                except ValueError:
                    break
        
        # Try to parse as plain number
        try:
            return int(size_str)
        except ValueError:
            return 0
    
    def prepare_usb_device(self, device: USBDevice, 
                          esp_size_mb: int = 512) -> Tuple[bool, str]:
        """
        Prepare USB device with proper partitioning
        تحضير جهاز USB مع التقسيم المناسب
        
        Args:
            device: USB device to prepare
            esp_size_mb: Size of ESP partition in MB
            
        Returns:
            Tuple of (success, message)
        """
        try:
            logger.info(f"Preparing USB device: {device}")
            
            if self.platform == "windows":
                return self._prepare_usb_windows(device, esp_size_mb)
            elif self.platform == "linux":
                return self._prepare_usb_linux(device, esp_size_mb)
            elif self.platform == "darwin":
                return self._prepare_usb_macos(device, esp_size_mb)
            else:
                return False, f"Unsupported platform: {self.platform}"
                
        except Exception as e:
            error_msg = f"Error preparing USB device: {e}"
            logger.error(error_msg)
            return False, error_msg
    
    def _prepare_usb_windows(self, device: USBDevice, esp_size_mb: int) -> Tuple[bool, str]:
        """Prepare USB device on Windows using diskpart"""
        try:
            # Create diskpart script
            script_content = f"""
select disk {self._get_disk_number_windows(device.device_path)}
clean
convert gpt
create partition efi size={esp_size_mb}
format quick fs=fat32 label="OSLLTN_ESP"
assign letter=S
active
create partition primary
format quick fs=exfat label="OSLLTN_DATA"
assign letter=D
exit
"""
            
            script_path = Path.cwd() / "temp_diskpart.txt"
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            # Run diskpart
            result = subprocess.run(
                ['diskpart', '/s', str(script_path)],
                capture_output=True, text=True, check=True
            )
            
            # Clean up
            script_path.unlink(missing_ok=True)
            
            logger.info("USB device prepared successfully on Windows")
            return True, "USB device prepared successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Diskpart failed: {e.stderr}"
        except Exception as e:
            return False, f"Error preparing USB on Windows: {e}"
    
    def _prepare_usb_linux(self, device: USBDevice, esp_size_mb: int) -> Tuple[bool, str]:
        """Prepare USB device on Linux using parted"""
        try:
            device_path = device.device_path
            
            # Unmount any mounted partitions
            self._unmount_device_linux(device_path)
            
            # Create GPT partition table
            subprocess.run(['sudo', 'parted', device_path, 'mklabel', 'gpt'], 
                         check=True, capture_output=True)
            
            # Create ESP partition
            subprocess.run(['sudo', 'parted', device_path, 'mkpart', 'ESP', 'fat32', 
                          '1MiB', f'{esp_size_mb}MiB'], check=True, capture_output=True)
            
            # Create data partition
            subprocess.run(['sudo', 'parted', device_path, 'mkpart', 'DATA', 'exfat', 
                          f'{esp_size_mb}MiB', '100%'], check=True, capture_output=True)
            
            # Set ESP partition as bootable
            subprocess.run(['sudo', 'parted', device_path, 'set', '1', 'esp', 'on'], 
                         check=True, capture_output=True)
            
            # Format partitions
            esp_partition = f"{device_path}1"
            data_partition = f"{device_path}2"
            
            subprocess.run(['sudo', 'mkfs.fat', '-F32', '-n', 'OSLLTN_ESP', esp_partition], 
                         check=True, capture_output=True)
            
            subprocess.run(['sudo', 'mkfs.exfat', '-n', 'OSLLTN_DATA', data_partition], 
                         check=True, capture_output=True)
            
            logger.info("USB device prepared successfully on Linux")
            return True, "USB device prepared successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"Partitioning failed: {e.stderr.decode()}"
        except Exception as e:
            return False, f"Error preparing USB on Linux: {e}"
    
    def _prepare_usb_macos(self, device: USBDevice, esp_size_mb: int) -> Tuple[bool, str]:
        """Prepare USB device on macOS using diskutil"""
        try:
            device_path = device.device_path
            
            # Unmount the device
            subprocess.run(['diskutil', 'unmountDisk', device_path], 
                         check=True, capture_output=True)
            
            # Partition the disk
            subprocess.run([
                'diskutil', 'partitionDisk', device_path, 'GPT',
                'MS-DOS', 'OSLLTN_ESP', f'{esp_size_mb}MB',
                'ExFAT', 'OSLLTN_DATA', 'R'
            ], check=True, capture_output=True)
            
            logger.info("USB device prepared successfully on macOS")
            return True, "USB device prepared successfully"
            
        except subprocess.CalledProcessError as e:
            return False, f"diskutil failed: {e.stderr.decode()}"
        except Exception as e:
            return False, f"Error preparing USB on macOS: {e}"
    
    def _get_disk_number_windows(self, drive_letter: str) -> str:
        """Get disk number for a drive letter on Windows"""
        try:
            result = subprocess.run(
                ['wmic', 'logicaldisk', 'where', f'DeviceID="{drive_letter}"', 
                 'get', 'DeviceID,DiskIndex', '/format:csv'],
                capture_output=True, text=True, check=True
            )
            
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # Skip header
                if drive_letter in line:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        return parts[1].strip()
            
            return "0"  # Default fallback
            
        except Exception as e:
            logger.warning(f"Could not get disk number for {drive_letter}: {e}")
            return "0"
    
    def _unmount_device_linux(self, device_path: str):
        """Unmount all partitions of a device on Linux"""
        try:
            # Get all partitions of the device
            result = subprocess.run(
                ['lsblk', '-ln', '-o', 'NAME', device_path],
                capture_output=True, text=True, check=True
            )
            
            for line in result.stdout.strip().split('\n'):
                partition = f"/dev/{line.strip()}"
                if partition != device_path:  # Don't try to unmount the main device
                    try:
                        subprocess.run(['sudo', 'umount', partition], 
                                     capture_output=True, check=False)
                    except:
                        pass  # Ignore errors, partition might not be mounted
                        
        except Exception as e:
            logger.warning(f"Could not unmount device {device_path}: {e}")
    
    def get_device_partitions(self, device: USBDevice) -> Dict[str, str]:
        """
        Get partition information for a device
        الحصول على معلومات الأقسام لجهاز معين
        
        Returns:
            Dictionary with partition info: {'esp': '/path/to/esp', 'data': '/path/to/data'}
        """
        partitions = {}
        
        try:
            if self.platform == "windows":
                # For Windows, we'll use the assigned drive letters
                partitions['esp'] = 'S:\\'
                partitions['data'] = 'D:\\'
            elif self.platform == "linux":
                partitions['esp'] = f"{device.device_path}1"
                partitions['data'] = f"{device.device_path}2"
            elif self.platform == "darwin":
                partitions['esp'] = f"{device.device_path}s1"
                partitions['data'] = f"{device.device_path}s2"
                
        except Exception as e:
            logger.error(f"Error getting device partitions: {e}")
        
        return partitions
