#!/usr/bin/env python3
"""
Quick run script for OSLLTN Multi-Boot USB Creator
سكريبت تشغيل سريع لنظام إنشاء فلاش USB متعدد الإقلاع

This script provides a simple way to run the application with proper error handling.
هذا السكريبت يوفر طريقة بسيطة لتشغيل التطبيق مع معالجة صحيحة للأخطاء.
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print("❌ خطأ: Python 3.8 أو أحدث مطلوب")
        print(f"Current version: {sys.version}")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_dependencies():
    """Check if required dependencies are installed"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    try:
        import psutil
    except ImportError:
        missing_deps.append("psutil")
    
    if missing_deps:
        print("❌ Missing dependencies / تبعيات مفقودة:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n📦 Install with / ثبت باستخدام:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_permissions():
    """Check if running with appropriate permissions"""
    import platform
    
    system = platform.system().lower()
    
    if system == "windows":
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin() != 0
            if not is_admin:
                print("⚠️  Warning: Not running as administrator")
                print("⚠️  تحذير: لا يتم التشغيل كمدير")
                print("Some features may not work properly")
                print("بعض الميزات قد لا تعمل بشكل صحيح")
                return False
        except:
            pass
    else:
        if os.geteuid() != 0:
            print("⚠️  Warning: Not running as root")
            print("⚠️  تحذير: لا يتم التشغيل كجذر")
            print("Some features may not work properly")
            print("بعض الميزات قد لا تعمل بشكل صحيح")
            print("Consider running with: sudo python run.py")
            print("فكر في التشغيل باستخدام: sudo python run.py")
            return False
    
    return True

def main():
    """Main function to run the application"""
    print("🚀 Starting OSLLTN Multi-Boot USB Creator...")
    print("🚀 بدء تشغيل نظام إنشاء فلاش USB متعدد الإقلاع...")
    print()
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit / اضغط Enter للخروج...")
        return 1
    
    # Check dependencies
    print("🔍 Checking dependencies / فحص التبعيات...")
    if not check_dependencies():
        input("Press Enter to exit / اضغط Enter للخروج...")
        return 1
    
    # Check permissions
    print("🔐 Checking permissions / فحص الصلاحيات...")
    check_permissions()  # Warning only, don't exit
    
    print("✅ All checks passed / جميع الفحوصات نجحت")
    print()
    
    # Add project root to Python path
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    try:
        # Import and run the main application
        print("🎯 Launching application / تشغيل التطبيق...")
        from main import main as app_main
        return app_main()
        
    except ImportError as e:
        print(f"❌ Import error / خطأ في الاستيراد: {e}")
        print("Make sure all files are in the correct location")
        print("تأكد من وجود جميع الملفات في الموقع الصحيح")
        return 1
        
    except KeyboardInterrupt:
        print("\n🛑 Application interrupted by user")
        print("🛑 تم إيقاف التطبيق من قبل المستخدم")
        return 0
        
    except Exception as e:
        print(f"❌ Unexpected error / خطأ غير متوقع: {e}")
        print("Please report this issue on GitHub")
        print("يرجى الإبلاغ عن هذه المشكلة على GitHub")
        return 1

if __name__ == "__main__":
    exit_code = main()
    
    if exit_code != 0:
        input("Press Enter to exit / اضغط Enter للخروج...")
    
    sys.exit(exit_code)
